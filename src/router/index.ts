import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import About from '../views/About.vue'
import GameView from '../views/GameView.vue'

const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/about', name: 'About', component: About },
  { path: '/game/:gameId', name: 'Game', component: GameView }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
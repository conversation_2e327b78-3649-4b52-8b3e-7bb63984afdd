import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import About from '../views/About.vue'
import GameView from '../views/GameView.vue'

const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/about', name: 'About', component: About },
  { path: '/game/:gameId', name: 'Game', component: GameView }
]

// 在开发环境中添加错误测试页面
if (import.meta.env.DEV) {
  const ErrorTest = () => import('../views/ErrorTest.vue')
  routes.push({
    path: '/error-test',
    name: 'ErrorTest',
    component: ErrorTest as any
  })
}

export default createRouter({
  history: createWebHistory(),
  routes
})
<template>
  <ErrorBoundary>
    <div id="app" class="min-h-screen bg-white">
      <MetaManager />
      <Navbar />
      <main class="flex-1">
        <router-view />
      </main>
      <Footer />
      <PWAInstallPrompt />
      <BrowserCompatCheck />
      <ResponsiveHelper v-if="isDev" />
      <PerformanceMonitor v-if="isDev" />
      <ErrorHandler ref="errorHandler" />
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
import Navbar from './components/Navbar.vue'
import Footer from './components/Footer.vue'
import PWAInstallPrompt from './components/PWAInstallPrompt.vue'
import BrowserCompatCheck from './components/BrowserCompatCheck.vue'
import ResponsiveHelper from './components/ResponsiveHelper.vue'
import MetaManager from './components/MetaManager.vue'
import PerformanceMonitor from './components/PerformanceMonitor.vue'
import ErrorHandler from './components/ErrorHandler.vue'
import ErrorBoundary from './components/ErrorBoundary.vue'

const isDev = ref(import.meta.env.DEV)
const errorHandler = ref<InstanceType<typeof ErrorHandler> | null>(null)

// Capture errors from child components
onErrorCaptured((err, instance, info) => {
  console.error('Error captured in App.vue:', err)

  if (errorHandler.value) {
    errorHandler.value.reportError(
      'An error occurred in the application',
      isDev.value ? `${err}\nComponent: ${instance?.$options?.name || 'Unknown'}\nInfo: ${info}` : undefined
    )
  }

  // Prevent the error from propagating further
  return false
})
</script>

<style>
#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>

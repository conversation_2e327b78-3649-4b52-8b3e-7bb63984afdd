<template>
  <div v-if="hasError" class="error-boundary">
    <div class="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
        </div>
        
        <h2 class="text-xl font-semibold text-gray-900 mb-2">
          {{ $t('error.title') || 'Something went wrong' }}
        </h2>
        
        <p class="text-gray-600 mb-6">
          {{ $t('error.description') || 'An unexpected error occurred. Please try refreshing the page.' }}
        </p>
        
        <div v-if="isDev && errorInfo" class="mb-6 text-left">
          <details class="bg-gray-50 rounded p-3">
            <summary class="cursor-pointer text-sm font-medium text-gray-700 mb-2">
              Error Details (Development)
            </summary>
            <pre class="text-xs text-gray-600 overflow-auto max-h-40">{{ errorInfo }}</pre>
          </details>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            @click="refreshPage"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
          >
            {{ $t('error.refresh') || 'Refresh Page' }}
          </button>
          
          <button
            @click="goHome"
            class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md transition-colors"
          >
            {{ $t('error.goHome') || 'Go Home' }}
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ErrorFilter } from '../utils/errorFilter'

const router = useRouter()
const hasError = ref(false)
const errorInfo = ref('')
const isDev = ref(import.meta.env.DEV)

// 捕获子组件错误
onErrorCaptured((err, instance, info) => {
  console.error('Error caught by ErrorBoundary:', err)
  
  hasError.value = true
  errorInfo.value = `${err.message}\n\nComponent: ${instance?.$options?.name || 'Unknown'}\nInfo: ${info}\n\nStack: ${err.stack}`
  
  // 阻止错误继续传播
  return false
})

// 监听全局错误
const handleGlobalError = (event: ErrorEvent) => {
  console.error('Global error caught by ErrorBoundary:', event.error)

  // 使用错误过滤器检查是否应该忽略此错误
  if (ErrorFilter.shouldIgnoreError(event.error || event.message)) {
    console.debug('ErrorBoundary: Ignoring filtered global error')
    return
  }

  hasError.value = true
  errorInfo.value = `${event.message}\n\nFile: ${event.filename}\nLine: ${event.lineno}\nColumn: ${event.colno}\n\nStack: ${event.error?.stack || 'No stack trace'}`
}

// 监听Promise rejection
const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
  console.error('Unhandled promise rejection caught by ErrorBoundary:', event.reason)

  // 使用错误过滤器检查是否应该忽略此错误
  if (ErrorFilter.shouldIgnoreError(event.reason)) {
    console.debug('ErrorBoundary: Ignoring filtered error')
    return
  }

  hasError.value = true

  if (event.reason instanceof Error) {
    errorInfo.value = `Promise Rejection: ${event.reason.message}\n\nStack: ${event.reason.stack}`
  } else {
    errorInfo.value = `Promise Rejection: ${JSON.stringify(event.reason, null, 2)}`
  }
}

const refreshPage = () => {
  window.location.reload()
}

const goHome = () => {
  hasError.value = false
  router.push('/')
}

onMounted(() => {
  window.addEventListener('error', handleGlobalError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
})

// 清理事件监听器
const cleanup = () => {
  window.removeEventListener('error', handleGlobalError)
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
}

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(cleanup)

// 暴露重置方法
defineExpose({
  reset: () => {
    hasError.value = false
    errorInfo.value = ''
  }
})
</script>

<style scoped>
.error-boundary {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.1);
}
</style>

<template>
  <!-- This component doesn't render anything -->
</template>

<script setup lang="ts">
import { watch, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

interface MetaInfo {
  title: string
  description: string
  keywords: string
  ogTitle: string
  ogDescription: string
  ogImage: string
  ogUrl: string
  twitterCard: string
}

const route = useRoute()

// 安全地使用i18n，避免初始化问题
let i18nInstance: any = null
try {
  i18nInstance = useI18n()
} catch (error) {
  console.warn('i18n not available in MetaManager:', error)
}

const locale = computed(() => i18nInstance?.locale?.value || 'en')

// Default meta information
const defaultMeta: MetaInfo = {
  title: 'Poy8 Card - Free Card Games',
  description: 'Play free card games online including Solitaire, Blackjack, Poker, Hearts, Spades, and Bridge.',
  keywords: 'card games, free games, online games, solitaire, poker, blackjack, hearts, spades, bridge',
  ogTitle: 'Poy8 Card - Free Card Games',
  ogDescription: 'Play free card games online including Solitaire, Blackjack, Poker, Hearts, Spades, and Bridge.',
  ogImage: '/assets/images/og-image.jpg',
  ogUrl: 'https://card.poy8.com',
  twitterCard: 'summary_large_image'
}

// Game-specific meta information
const gameMeta: Record<string, Partial<MetaInfo>> = {
  solitaire: {
    title: 'Solitaire - Poy8 Card Games',
    description: 'Play classic Solitaire card game for free. Arrange cards in descending order and alternating colors to win.',
    keywords: 'solitaire, patience, klondike, card game, free game',
    ogTitle: 'Play Solitaire Online - Poy8 Card Games',
    ogDescription: 'Play classic Solitaire card game for free. No download required.',
    ogImage: '/assets/images/solitaire-og.jpg'
  },
  blackjack: {
    title: 'Blackjack - Poy8 Card Games',
    description: 'Play Blackjack online for free. Beat the dealer by getting as close to 21 as possible without going over.',
    keywords: 'blackjack, 21, card game, casino game, free game',
    ogTitle: 'Play Blackjack Online - Poy8 Card Games',
    ogDescription: 'Play Blackjack online for free. No download required.',
    ogImage: '/assets/images/blackjack-og.jpg'
  },
  poker: {
    title: 'Poker - Poy8 Card Games',
    description: 'Play Texas Hold\'em Poker online for free. Make the best five-card hand to win the pot.',
    keywords: 'poker, texas holdem, card game, casino game, free game',
    ogTitle: 'Play Poker Online - Poy8 Card Games',
    ogDescription: 'Play Texas Hold\'em Poker online for free. No download required.',
    ogImage: '/assets/images/poker-og.jpg'
  },
  hearts: {
    title: 'Hearts - Poy8 Card Games',
    description: 'Play Hearts card game online for free. Avoid taking hearts and the Queen of Spades.',
    keywords: 'hearts, card game, trick-taking game, free game',
    ogTitle: 'Play Hearts Online - Poy8 Card Games',
    ogDescription: 'Play Hearts card game online for free. No download required.',
    ogImage: '/assets/images/hearts-og.jpg'
  },
  spades: {
    title: 'Spades - Poy8 Card Games',
    description: 'Play Spades card game online for free. Bid and make your contract to score points.',
    keywords: 'spades, card game, trick-taking game, bidding game, free game',
    ogTitle: 'Play Spades Online - Poy8 Card Games',
    ogDescription: 'Play Spades card game online for free. No download required.',
    ogImage: '/assets/images/spades-og.jpg'
  },
  bridge: {
    title: 'Bridge - Poy8 Card Games',
    description: 'Play Bridge card game online for free. The ultimate partnership card game.',
    keywords: 'bridge, card game, trick-taking game, bidding game, free game',
    ogTitle: 'Play Bridge Online - Poy8 Card Games',
    ogDescription: 'Play Bridge card game online for free. No download required.',
    ogImage: '/assets/images/bridge-og.jpg'
  }
}

// Update meta tags
const updateMetaTags = () => {
  let meta: MetaInfo = { ...defaultMeta }
  
  // Check if we're on a game page
  if (route.name === 'Game' && typeof route.params.gameId === 'string') {
    const gameId = route.params.gameId
    if (gameId in gameMeta) {
      meta = { ...meta, ...gameMeta[gameId] }
    }
  }
  
  // Update document title
  document.title = meta.title
  
  // Update meta tags
  updateMetaTag('description', meta.description)
  updateMetaTag('keywords', meta.keywords)
  
  // Update Open Graph tags
  updateMetaTag('og:title', meta.ogTitle)
  updateMetaTag('og:description', meta.ogDescription)
  updateMetaTag('og:image', meta.ogImage)
  updateMetaTag('og:url', `${meta.ogUrl}${route.path}`)
  updateMetaTag('og:type', 'website')
  
  // Update Twitter Card tags
  updateMetaTag('twitter:card', meta.twitterCard)
  updateMetaTag('twitter:title', meta.ogTitle)
  updateMetaTag('twitter:description', meta.ogDescription)
  updateMetaTag('twitter:image', meta.ogImage)
  
  // Update language
  updateMetaTag('og:locale', locale.value)
  document.documentElement.lang = locale.value
}

// Helper function to update or create meta tags
const updateMetaTag = (name: string, content: string) => {
  let meta: HTMLMetaElement | null = null
  
  // Check if it's an Open Graph or Twitter tag
  if (name.startsWith('og:')) {
    meta = document.querySelector(`meta[property="${name}"]`)
  } else if (name.startsWith('twitter:')) {
    meta = document.querySelector(`meta[name="${name}"]`)
  } else {
    meta = document.querySelector(`meta[name="${name}"]`)
  }
  
  if (meta) {
    meta.setAttribute('content', content)
  } else {
    meta = document.createElement('meta')
    
    if (name.startsWith('og:')) {
      meta.setAttribute('property', name)
    } else {
      meta.setAttribute('name', name)
    }
    
    meta.setAttribute('content', content)
    document.head.appendChild(meta)
  }
}

// Watch for route changes
watch(
  () => route.path,
  () => {
    updateMetaTags()
  }
)

// Watch for language changes
watch(
  locale,
  () => {
    updateMetaTags()
  }
)

onMounted(() => {
  updateMetaTags()
})

onUnmounted(() => {
  // Clean up any meta tags if needed
})
</script>

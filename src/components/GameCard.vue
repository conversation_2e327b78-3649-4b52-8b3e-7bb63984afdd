<template>
  <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
    <!-- Game Image -->
    <div class="relative h-48 bg-gradient-to-br from-blue-100 to-blue-200">
      <img 
        v-if="game.image" 
        :src="game.image" 
        :alt="game.name"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-full h-full flex items-center justify-center">
        <div class="text-6xl">{{ game.icon }}</div>
      </div>
      
      <!-- Difficulty Badge -->
      <div class="absolute top-3 right-3">
        <span 
          class="px-2 py-1 text-xs font-medium rounded-full"
          :class="difficultyClass"
        >
          {{ game.difficulty }}
        </span>
      </div>
    </div>

    <!-- Game Content -->
    <div class="p-6">
      <h3 class="text-xl font-semibold text-gray-900 mb-2">
        {{ $t(`games.${game.id}`) }}
      </h3>
      
      <p class="text-gray-600 text-sm mb-4 line-clamp-3">
        {{ game.description }}
      </p>

      <!-- Game Stats -->
      <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
        <span>{{ game.players }} players</span>
        <span>{{ game.duration }} min</span>
      </div>

      <!-- Action Button -->
      <button
        @click="startGame"
        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"></path>
        </svg>
        <span>{{ $t('games.startGame') }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'

interface Game {
  id: string
  name: string
  description: string
  image?: string
  icon: string
  difficulty: 'Easy' | 'Medium' | 'Hard'
  players: string
  duration: number
}

const props = defineProps<{
  game: Game
}>()

const router = useRouter()

const difficultyClass = computed(() => {
  switch (props.game.difficulty) {
    case 'Easy':
      return 'bg-green-100 text-green-800'
    case 'Medium':
      return 'bg-yellow-100 text-yellow-800'
    case 'Hard':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})

const startGame = () => {
  router.push(`/game/${props.game.id}`)
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

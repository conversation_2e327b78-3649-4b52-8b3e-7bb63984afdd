<template>
  <div class="i18n-test p-6 bg-white rounded-lg shadow-md">
    <h3 class="text-lg font-semibold mb-4">🌍 i18n Test Component</h3>
    
    <div class="space-y-4">
      <div>
        <strong>Current Locale:</strong> {{ currentLocale }}
      </div>
      
      <div>
        <strong>Navigation:</strong>
        <ul class="list-disc pl-5">
          <li>{{ $t('nav.home') }}</li>
          <li>{{ $t('nav.about') }}</li>
        </ul>
      </div>
      
      <div>
        <strong>Games:</strong>
        <ul class="list-disc pl-5">
          <li>{{ $t('games.solitaire.name') }}</li>
          <li>{{ $t('games.blackjack.name') }}</li>
          <li>{{ $t('games.poker.name') }}</li>
          <li>{{ $t('games.hearts.name') }}</li>
          <li>{{ $t('games.spades.name') }}</li>
          <li>{{ $t('games.bridge.name') }}</li>
        </ul>
      </div>
      
      <div>
        <strong>Common:</strong>
        <ul class="list-disc pl-5">
          <li>{{ $t('common.loading') }}</li>
          <li>{{ $t('common.error') }}</li>
          <li>{{ $t('common.retry') }}</li>
          <li>{{ $t('common.close') }}</li>
        </ul>
      </div>
      
      <div>
        <strong>Error Messages:</strong>
        <ul class="list-disc pl-5">
          <li>{{ $t('error.title') }}</li>
          <li>{{ $t('error.description') }}</li>
          <li>{{ $t('error.refresh') }}</li>
          <li>{{ $t('error.goHome') }}</li>
        </ul>
      </div>
      
      <div class="mt-6">
        <h4 class="font-semibold mb-2">Quick Language Test:</h4>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="lang in availableLanguages"
            :key="lang.code"
            @click="changeLanguage(lang.code)"
            :class="currentLocale === lang.code ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'"
            class="px-3 py-1 rounded text-sm transition-colors hover:bg-blue-500 hover:text-white"
          >
            {{ lang.flag }} {{ lang.name }}
          </button>
        </div>
      </div>
      
      <div class="mt-4 p-3 bg-gray-50 rounded">
        <div class="text-sm text-gray-600">
          <strong>Test Result:</strong> 
          <span v-if="i18nWorking" class="text-green-600">✅ i18n is working correctly!</span>
          <span v-else class="text-red-600">❌ i18n has issues</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { locale, t } = useI18n()

const currentLocale = computed(() => locale.value)

const availableLanguages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'es', name: 'Español', flag: '🇪🇸' },
  { code: 'pt', name: 'Português', flag: '🇵🇹' },
  { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
  { code: 'la', name: 'Latina', flag: '🏛️' }
]

const i18nWorking = computed(() => {
  try {
    // 测试基本的翻译功能
    const homeText = t('nav.home')
    const loadingText = t('common.loading')
    
    // 检查是否返回了有效的翻译（不是key本身）
    return homeText !== 'nav.home' && loadingText !== 'common.loading'
  } catch (error) {
    console.error('i18n test error:', error)
    return false
  }
})

const changeLanguage = (langCode: string) => {
  locale.value = langCode
  
  // 保存到localStorage
  localStorage.setItem('language', langCode)
  
  console.log(`Language changed to: ${langCode}`)
}
</script>

<style scoped>
.i18n-test {
  font-family: 'Inter', sans-serif;
}
</style>

<template>
  <footer class="bg-white border-t border-gray-200 mt-auto">
    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Brand Section -->
        <div class="col-span-1">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Poy8 Card</h3>
          <p class="text-gray-600 text-sm">
            {{ $t('about.description') }}
          </p>
        </div>

        <!-- Quick Links -->
        <div class="col-span-1">
          <h4 class="text-md font-medium text-gray-900 mb-4">Quick Links</h4>
          <ul class="space-y-2">
            <li>
              <router-link to="/" class="text-gray-600 hover:text-gray-900 text-sm transition-colors">
                {{ $t('nav.home') }}
              </router-link>
            </li>
            <li>
              <router-link to="/about" class="text-gray-600 hover:text-gray-900 text-sm transition-colors">
                {{ $t('nav.about') }}
              </router-link>
            </li>
            <li>
              <a href="https://poy8.com" target="_blank" class="text-gray-600 hover:text-gray-900 text-sm transition-colors">
                {{ $t('nav.poy8') }}
              </a>
            </li>
          </ul>
        </div>

        <!-- Features -->
        <div class="col-span-1">
          <h4 class="text-md font-medium text-gray-900 mb-4">{{ $t('about.features') }}</h4>
          <ul class="space-y-2 text-sm text-gray-600">
            <li>{{ $t('about.feature1') }}</li>
            <li>{{ $t('about.feature2') }}</li>
            <li>{{ $t('about.feature3') }}</li>
            <li>{{ $t('about.feature4') }}</li>
          </ul>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="mt-8 pt-8 border-t border-gray-200 flex flex-col sm:flex-row justify-between items-center">
        <p class="text-gray-500 text-sm">
          {{ $t('footer.copyright') }}
        </p>
        
        <button
          @click="scrollToTop"
          class="mt-4 sm:mt-0 text-gray-500 hover:text-gray-700 text-sm transition-colors flex items-center space-x-1"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
          </svg>
          <span>{{ $t('footer.backToTop') }}</span>
        </button>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}
</script>

<template>
  <div class="poker-game h-full bg-green-800 p-4">
    <!-- Game Header -->
    <div class="flex justify-between items-center mb-6 text-white">
      <div class="flex items-center space-x-4">
        <span>Chips: ${{ playerChips }}</span>
        <span>Pot: ${{ pot }}</span>
        <span>Round: {{ round }}</span>
      </div>
      <button
        @click="newGame"
        class="bg-green-900 hover:bg-green-950 px-4 py-2 rounded text-white transition-colors"
      >
        New Game
      </button>
    </div>

    <!-- Community Cards -->
    <div class="community-section mb-8 text-center">
      <h3 class="text-white text-lg mb-4">Community Cards</h3>
      <div class="flex justify-center space-x-2">
        <PlayingCard
          v-for="(card, index) in communityCards"
          :key="`community-${index}`"
          :card="card"
          :draggable="false"
        />
        <div
          v-for="n in (5 - communityCards.length)"
          :key="`empty-${n}`"
          class="w-20 h-28 border-2 border-dashed border-white rounded-lg flex items-center justify-center text-white text-xs"
        >
          ?
        </div>
      </div>
    </div>

    <!-- Opponent -->
    <div class="opponent-section mb-8">
      <h3 class="text-white text-lg mb-2">Computer (Chips: ${{ computerChips }})</h3>
      <div class="flex space-x-2">
        <PlayingCard
          v-for="(card, index) in computerCards"
          :key="`computer-${index}`"
          :card="showdown ? card : { ...card, faceUp: false }"
          :draggable="false"
        />
      </div>
      <div v-if="computerAction" class="text-yellow-300 mt-2">
        Computer: {{ computerAction }}
      </div>
    </div>

    <!-- Player -->
    <div class="player-section mb-8">
      <h3 class="text-white text-lg mb-2">Your Hand</h3>
      <div class="flex space-x-2 mb-4">
        <PlayingCard
          v-for="(card, index) in playerCards"
          :key="`player-${index}`"
          :card="card"
          :draggable="false"
        />
      </div>
      <div v-if="playerHandRank" class="text-yellow-300">
        Your Hand: {{ playerHandRank }}
      </div>
    </div>

    <!-- Betting Actions -->
    <div v-if="gameState === 'betting' && !gameOver" class="betting-section mb-6">
      <div class="flex items-center space-x-4 mb-4">
        <span class="text-white">Current Bet: ${{ currentBet }}</span>
        <span class="text-white">To Call: ${{ toCall }}</span>
      </div>
      
      <div class="flex space-x-4">
        <button
          v-if="toCall === 0"
          @click="check"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded transition-colors"
        >
          Check
        </button>
        <button
          v-if="toCall > 0"
          @click="call"
          :disabled="playerChips < toCall"
          class="bg-green-600 hover:bg-green-700 disabled:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
        >
          Call ${{ toCall }}
        </button>
        <button
          @click="showRaiseInput = !showRaiseInput"
          :disabled="playerChips <= toCall"
          class="bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
        >
          Raise
        </button>
        <button
          @click="fold"
          class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded transition-colors"
        >
          Fold
        </button>
      </div>

      <div v-if="showRaiseInput" class="mt-4 flex items-center space-x-4">
        <input
          v-model.number="raiseAmount"
          type="number"
          :min="toCall + 10"
          :max="playerChips"
          class="bg-white text-black px-3 py-2 rounded w-32"
          placeholder="Raise amount"
        />
        <button
          @click="raise"
          :disabled="raiseAmount < toCall + 10 || raiseAmount > playerChips"
          class="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-500 text-white px-4 py-2 rounded transition-colors"
        >
          Raise ${{ raiseAmount }}
        </button>
      </div>
    </div>

    <!-- Game Result -->
    <div v-if="gameOver" class="result-section mb-6">
      <div class="bg-white rounded-lg p-6 text-center">
        <h2 class="text-2xl font-bold mb-2" :class="resultColor">{{ gameResult }}</h2>
        <p class="text-gray-600 mb-4">{{ resultMessage }}</p>
        <button
          @click="nextRound"
          :disabled="playerChips <= 0"
          class="bg-green-600 hover:bg-green-700 disabled:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
        >
          {{ playerChips <= 0 ? 'Game Over' : 'Next Round' }}
        </button>
      </div>
    </div>

    <!-- Game Over Modal -->
    <div v-if="playerChips <= 0" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 text-center">
        <h2 class="text-2xl font-bold text-red-600 mb-4">Game Over!</h2>
        <p class="text-gray-600 mb-4">You're out of chips!</p>
        <p class="text-gray-600 mb-4">You played {{ round }} rounds.</p>
        <button
          @click="resetGame"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded transition-colors"
        >
          Start Over
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import PlayingCard from './PlayingCard.vue'

const emit = defineEmits<{
  'game-over': [result: any]
}>()

interface Card {
  suit: string
  rank: string
  faceUp: boolean
  color: 'red' | 'black'
  value: number
}

const playerChips = ref(1000)
const computerChips = ref(1000)
const pot = ref(0)
const round = ref(1)

const gameState = ref<'dealing' | 'betting' | 'showdown'>('dealing')
const gameOver = ref(false)
const showdown = ref(false)

const playerCards = ref<Card[]>([])
const computerCards = ref<Card[]>([])
const communityCards = ref<Card[]>([])
const deck = ref<Card[]>([])

const currentBet = ref(0)
const toCall = ref(0)
const showRaiseInput = ref(false)
const raiseAmount = ref(0)

const computerAction = ref('')
const gameResult = ref('')
const resultMessage = ref('')

const suits = ['♠', '♥', '♦', '♣']
const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']

const createDeck = (): Card[] => {
  const newDeck: Card[] = []
  suits.forEach(suit => {
    ranks.forEach((rank, index) => {
      newDeck.push({
        suit,
        rank,
        faceUp: true,
        color: suit === '♥' || suit === '♦' ? 'red' : 'black',
        value: index + 2
      })
    })
  })
  return shuffleDeck(newDeck)
}

const shuffleDeck = (deckToShuffle: Card[]): Card[] => {
  const shuffled = [...deckToShuffle]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const evaluateHand = (cards: Card[]): { rank: number; name: string } => {
  if (cards.length < 5) return { rank: 0, name: 'High Card' }
  
  const sortedCards = [...cards].sort((a, b) => b.value - a.value)
  const ranks = sortedCards.map(card => card.value)
  const suits = sortedCards.map(card => card.suit)
  
  // Count ranks
  const rankCounts: { [key: number]: number } = {}
  ranks.forEach(rank => {
    rankCounts[rank] = (rankCounts[rank] || 0) + 1
  })
  
  const counts = Object.values(rankCounts).sort((a, b) => b - a)
  const isFlush = suits.every(suit => suit === suits[0])
  const isStraight = ranks.every((rank, index) => index === 0 || rank === ranks[index - 1] - 1)
  
  if (isFlush && isStraight) return { rank: 8, name: 'Straight Flush' }
  if (counts[0] === 4) return { rank: 7, name: 'Four of a Kind' }
  if (counts[0] === 3 && counts[1] === 2) return { rank: 6, name: 'Full House' }
  if (isFlush) return { rank: 5, name: 'Flush' }
  if (isStraight) return { rank: 4, name: 'Straight' }
  if (counts[0] === 3) return { rank: 3, name: 'Three of a Kind' }
  if (counts[0] === 2 && counts[1] === 2) return { rank: 2, name: 'Two Pair' }
  if (counts[0] === 2) return { rank: 1, name: 'One Pair' }
  
  return { rank: 0, name: 'High Card' }
}

const playerHandRank = computed(() => {
  if (playerCards.value.length === 2 && communityCards.value.length >= 3) {
    const allCards = [...playerCards.value, ...communityCards.value]
    return evaluateHand(allCards).name
  }
  return ''
})

const resultColor = computed(() => {
  if (gameResult.value.includes('Win')) return 'text-green-600'
  if (gameResult.value.includes('Lose')) return 'text-red-600'
  return 'text-yellow-600'
})

const dealCards = () => {
  deck.value = createDeck()
  playerCards.value = []
  computerCards.value = []
  communityCards.value = []
  
  // Deal hole cards
  for (let i = 0; i < 2; i++) {
    playerCards.value.push(deck.value.pop()!)
    computerCards.value.push(deck.value.pop()!)
  }
  
  // Deal flop
  setTimeout(() => {
    for (let i = 0; i < 3; i++) {
      communityCards.value.push(deck.value.pop()!)
    }
    gameState.value = 'betting'
  }, 1000)
}

const check = () => {
  computerPlay()
}

const call = () => {
  playerChips.value -= toCall.value
  pot.value += toCall.value
  currentBet.value = Math.max(currentBet.value, toCall.value)
  toCall.value = 0
  computerPlay()
}

const raise = () => {
  if (raiseAmount.value >= toCall.value + 10 && raiseAmount.value <= playerChips.value) {
    playerChips.value -= raiseAmount.value
    pot.value += raiseAmount.value
    currentBet.value = raiseAmount.value
    toCall.value = raiseAmount.value
    showRaiseInput.value = false
    computerPlay()
  }
}

const fold = () => {
  gameResult.value = 'You Lose!'
  resultMessage.value = 'You folded.'
  computerChips.value += pot.value
  pot.value = 0
  gameOver.value = true
}

const computerPlay = () => {
  // Simple AI logic
  const random = Math.random()
  
  if (toCall.value === 0) {
    if (random < 0.7) {
      computerAction.value = 'checks'
      nextPhase()
    } else {
      const betAmount = Math.min(50, computerChips.value)
      computerChips.value -= betAmount
      pot.value += betAmount
      currentBet.value = betAmount
      toCall.value = betAmount
      computerAction.value = `bets $${betAmount}`
    }
  } else {
    if (random < 0.6) {
      computerChips.value -= toCall.value
      pot.value += toCall.value
      computerAction.value = `calls $${toCall.value}`
      toCall.value = 0
      nextPhase()
    } else {
      computerAction.value = 'folds'
      gameResult.value = 'You Win!'
      resultMessage.value = 'Computer folded.'
      playerChips.value += pot.value
      pot.value = 0
      gameOver.value = true
    }
  }
}

const nextPhase = () => {
  if (communityCards.value.length === 3) {
    // Deal turn
    setTimeout(() => {
      communityCards.value.push(deck.value.pop()!)
      currentBet.value = 0
      toCall.value = 0
    }, 1000)
  } else if (communityCards.value.length === 4) {
    // Deal river
    setTimeout(() => {
      communityCards.value.push(deck.value.pop()!)
      currentBet.value = 0
      toCall.value = 0
    }, 1000)
  } else {
    // Showdown
    showdown.value = true
    determineWinner()
  }
}

const determineWinner = () => {
  const playerHand = evaluateHand([...playerCards.value, ...communityCards.value])
  const computerHand = evaluateHand([...computerCards.value, ...communityCards.value])
  
  if (playerHand.rank > computerHand.rank) {
    gameResult.value = 'You Win!'
    resultMessage.value = `Your ${playerHand.name} beats computer's ${computerHand.name}`
    playerChips.value += pot.value
  } else if (computerHand.rank > playerHand.rank) {
    gameResult.value = 'You Lose!'
    resultMessage.value = `Computer's ${computerHand.name} beats your ${playerHand.name}`
    computerChips.value += pot.value
  } else {
    gameResult.value = 'Tie!'
    resultMessage.value = `Both have ${playerHand.name}`
    playerChips.value += Math.floor(pot.value / 2)
    computerChips.value += Math.floor(pot.value / 2)
  }
  
  pot.value = 0
  gameOver.value = true
}

const nextRound = () => {
  if (playerChips.value <= 0) {
    emit('game-over', { rounds: round.value })
    return
  }
  
  round.value++
  gameOver.value = false
  showdown.value = false
  gameState.value = 'dealing'
  computerAction.value = ''
  currentBet.value = 0
  toCall.value = 0
  showRaiseInput.value = false
  
  dealCards()
}

const newGame = () => {
  resetGame()
}

const resetGame = () => {
  playerChips.value = 1000
  computerChips.value = 1000
  round.value = 1
  pot.value = 0
  gameOver.value = false
  showdown.value = false
  gameState.value = 'dealing'
  computerAction.value = ''
  currentBet.value = 0
  toCall.value = 0
  showRaiseInput.value = false
  
  dealCards()
}

// Start first game
dealCards()
</script>

<style scoped>
.poker-game {
  user-select: none;
}
</style>

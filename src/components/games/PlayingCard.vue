<template>
  <div
    class="playing-card"
    :class="cardClasses"
    :draggable="draggable && card.faceUp"
    @dragstart="$emit('dragstart', $event)"
    @click="$emit('click')"
  >
    <div v-if="card.faceUp" class="card-face">
      <div class="card-corner top-left">
        <div class="rank">{{ card.rank }}</div>
        <div class="suit">{{ card.suit }}</div>
      </div>
      
      <div class="card-center">
        <div class="suit-large">{{ card.suit }}</div>
      </div>
      
      <div class="card-corner bottom-right">
        <div class="rank">{{ card.rank }}</div>
        <div class="suit">{{ card.suit }}</div>
      </div>
    </div>
    
    <div v-else class="card-back">
      <div class="back-pattern"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Card {
  suit: string
  rank: string
  faceUp: boolean
  color?: 'red' | 'black'
}

const props = defineProps<{
  card: Card
  draggable?: boolean
}>()

defineEmits<{
  dragstart: [event: DragEvent]
  click: []
}>()

const cardClasses = computed(() => ({
  'face-up': props.card.faceUp,
  'face-down': !props.card.faceUp,
  'red-card': props.card.color === 'red',
  'black-card': props.card.color === 'black',
  'draggable': props.draggable && props.card.faceUp
}))
</script>

<style scoped>
.playing-card {
  width: 80px;
  height: 112px;
  border-radius: 8px;
  border: 1px solid #333;
  background: white;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.playing-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.playing-card.draggable {
  cursor: grab;
}

.playing-card.draggable:active {
  cursor: grabbing;
}

.card-face {
  width: 100%;
  height: 100%;
  padding: 4px;
  position: relative;
}

.card-corner {
  position: absolute;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
}

.top-left {
  top: 4px;
  left: 4px;
}

.bottom-right {
  bottom: 4px;
  right: 4px;
  transform: rotate(180deg);
}

.card-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.suit-large {
  font-size: 24px;
}

.red-card .suit,
.red-card .rank,
.red-card .suit-large {
  color: #dc2626;
}

.black-card .suit,
.black-card .rank,
.black-card .suit-large {
  color: #1f2937;
}

.card-back {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #1e40af, #3b82f6);
  border-radius: 7px;
  position: relative;
  overflow: hidden;
}

.back-pattern {
  width: 100%;
  height: 100%;
  background-image: 
    repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1) 0px,
      rgba(255, 255, 255, 0.1) 2px,
      transparent 2px,
      transparent 4px
    ),
    repeating-linear-gradient(
      -45deg,
      rgba(255, 255, 255, 0.1) 0px,
      rgba(255, 255, 255, 0.1) 2px,
      transparent 2px,
      transparent 4px
    );
}

.face-down {
  cursor: pointer;
}

.face-down:hover {
  transform: translateY(-1px);
}
</style>

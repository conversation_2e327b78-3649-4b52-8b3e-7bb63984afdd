<template>
  <div class="hearts-game h-full bg-green-700 p-4">
    <!-- Game Header -->
    <div class="flex justify-between items-center mb-6 text-white">
      <div class="flex items-center space-x-6">
        <span>Round: {{ currentRound }}</span>
        <span>Trick: {{ currentTrick }}/13</span>
        <span v-if="gamePhase === 'passing'">Passing Phase</span>
        <span v-else-if="gamePhase === 'playing'">Playing Phase</span>
      </div>
      <button
        @click="newGame"
        class="bg-green-800 hover:bg-green-900 px-4 py-2 rounded text-white transition-colors"
      >
        New Game
      </button>
    </div>

    <!-- Scores -->
    <div class="scores-section mb-6 bg-white rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-2">Scores</h3>
      <div class="grid grid-cols-4 gap-4 text-center">
        <div v-for="(player, index) in players" :key="index" class="p-2 rounded" :class="index === 0 ? 'bg-blue-100' : 'bg-gray-100'">
          <div class="font-medium">{{ player.name }}</div>
          <div class="text-lg font-bold" :class="player.score >= 100 ? 'text-red-600' : 'text-gray-700'">
            {{ player.score }}
          </div>
        </div>
      </div>
    </div>

    <!-- Passing Phase -->
    <div v-if="gamePhase === 'passing'" class="passing-section mb-6">
      <div class="bg-white rounded-lg p-4 mb-4">
        <h3 class="text-lg font-semibold mb-2">Select 3 cards to pass {{ passDirection }}</h3>
        <p class="text-gray-600 text-sm">Click on 3 cards to select them for passing</p>
      </div>
      
      <div class="player-hand flex flex-wrap justify-center gap-2">
        <PlayingCard
          v-for="(card, index) in playerHand"
          :key="`hand-${index}`"
          :card="card"
          :draggable="false"
          :class="{ 'ring-2 ring-blue-500': selectedForPassing.includes(index) }"
          @click="toggleCardSelection(index)"
        />
      </div>
      
      <div class="text-center mt-4">
        <button
          @click="confirmPass"
          :disabled="selectedForPassing.length !== 3"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded transition-colors"
        >
          Pass Cards ({{ selectedForPassing.length }}/3)
        </button>
      </div>
    </div>

    <!-- Playing Phase -->
    <div v-if="gamePhase === 'playing'" class="playing-section">
      <!-- Current Trick -->
      <div class="trick-area mb-6">
        <h3 class="text-white text-lg mb-4 text-center">Current Trick</h3>
        <div class="relative w-80 h-80 mx-auto bg-green-600 rounded-full border-4 border-white">
          <!-- North (Computer 1) -->
          <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
            <PlayingCard
              v-if="currentTrickCards[1]"
              :card="currentTrickCards[1]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[1].name }}</div>
          </div>
          
          <!-- East (Computer 2) -->
          <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
            <PlayingCard
              v-if="currentTrickCards[2]"
              :card="currentTrickCards[2]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[2].name }}</div>
          </div>
          
          <!-- South (Player) -->
          <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <PlayingCard
              v-if="currentTrickCards[0]"
              :card="currentTrickCards[0]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[0].name }}</div>
          </div>
          
          <!-- West (Computer 3) -->
          <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
            <PlayingCard
              v-if="currentTrickCards[3]"
              :card="currentTrickCards[3]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[3].name }}</div>
          </div>
        </div>
      </div>

      <!-- Player Hand -->
      <div class="player-hand-section">
        <h3 class="text-white text-lg mb-4 text-center">Your Hand</h3>
        <div class="flex flex-wrap justify-center gap-2">
          <PlayingCard
            v-for="(card, index) in playerHand"
            :key="`hand-${index}`"
            :card="card"
            :draggable="false"
            :class="{ 
              'cursor-pointer hover:transform hover:-translate-y-2 transition-transform': canPlayCard(card),
              'opacity-50 cursor-not-allowed': !canPlayCard(card)
            }"
            @click="playCard(index)"
          />
        </div>
      </div>
    </div>

    <!-- Round End -->
    <div v-if="gamePhase === 'roundEnd'" class="round-end-section">
      <div class="bg-white rounded-lg p-6 text-center mb-6">
        <h2 class="text-2xl font-bold mb-4">Round {{ currentRound }} Complete</h2>
        <div class="grid grid-cols-4 gap-4 mb-4">
          <div v-for="(player, index) in players" :key="index" class="p-2 rounded bg-gray-100">
            <div class="font-medium">{{ player.name }}</div>
            <div class="text-sm text-gray-600">+{{ player.roundScore }} points</div>
            <div class="font-bold">Total: {{ player.score }}</div>
          </div>
        </div>
        <button
          @click="nextRound"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded transition-colors"
        >
          Next Round
        </button>
      </div>
    </div>

    <!-- Game Over -->
    <div v-if="gamePhase === 'gameOver'" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 text-center">
        <h2 class="text-2xl font-bold text-green-600 mb-4">Game Over!</h2>
        <div class="mb-4">
          <h3 class="text-lg font-semibold mb-2">Final Scores:</h3>
          <div class="space-y-2">
            <div v-for="(player, index) in sortedPlayers" :key="index" class="flex justify-between items-center">
              <span>{{ player.name }}</span>
              <span class="font-bold">{{ player.score }} points</span>
            </div>
          </div>
        </div>
        <p class="text-gray-600 mb-4">{{ winner.name }} wins with {{ winner.score }} points!</p>
        <button
          @click="resetGame"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded transition-colors"
        >
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import PlayingCard from './PlayingCard.vue'

const emit = defineEmits<{
  'game-over': [result: any]
}>()

interface Card {
  suit: string
  rank: string
  faceUp: boolean
  color: 'red' | 'black'
  value: number
}

interface Player {
  name: string
  score: number
  roundScore: number
  hand: Card[]
  tricksTaken: Card[][]
}

const currentRound = ref(1)
const currentTrick = ref(1)
const gamePhase = ref<'passing' | 'playing' | 'roundEnd' | 'gameOver'>('passing')

const players = ref<Player[]>([
  { name: 'You', score: 0, roundScore: 0, hand: [], tricksTaken: [] },
  { name: 'North', score: 0, roundScore: 0, hand: [], tricksTaken: [] },
  { name: 'East', score: 0, roundScore: 0, hand: [], tricksTaken: [] },
  { name: 'West', score: 0, roundScore: 0, hand: [], tricksTaken: [] }
])

const playerHand = computed(() => players.value[0].hand)
const selectedForPassing = ref<number[]>([])
const currentTrickCards = ref<(Card | null)[]>([null, null, null, null])
const currentPlayer = ref(0)
const trickLeader = ref(0)
const heartsBroken = ref(false)

const suits = ['♠', '♥', '♦', '♣']
const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']

const passDirection = computed(() => {
  const directions = ['left', 'right', 'across', 'no passing']
  return directions[(currentRound.value - 1) % 4]
})

const sortedPlayers = computed(() => {
  return [...players.value].sort((a, b) => a.score - b.score)
})

const winner = computed(() => sortedPlayers.value[0])

const createDeck = (): Card[] => {
  const deck: Card[] = []
  suits.forEach(suit => {
    ranks.forEach((rank, index) => {
      deck.push({
        suit,
        rank,
        faceUp: true,
        color: suit === '♥' || suit === '♦' ? 'red' : 'black',
        value: index + 2
      })
    })
  })
  return shuffleDeck(deck)
}

const shuffleDeck = (deck: Card[]): Card[] => {
  const shuffled = [...deck]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const dealCards = () => {
  const deck = createDeck()
  
  // Clear hands
  players.value.forEach(player => {
    player.hand = []
    player.tricksTaken = []
    player.roundScore = 0
  })
  
  // Deal 13 cards to each player
  for (let i = 0; i < 13; i++) {
    players.value.forEach(player => {
      player.hand.push(deck.pop()!)
    })
  }
  
  // Sort hands
  players.value.forEach(player => {
    player.hand.sort((a, b) => {
      if (a.suit !== b.suit) {
        return suits.indexOf(a.suit) - suits.indexOf(b.suit)
      }
      return a.value - b.value
    })
  })
  
  // Find player with 2 of clubs to start
  for (let i = 0; i < 4; i++) {
    const has2Clubs = players.value[i].hand.some(card => card.suit === '♣' && card.rank === '2')
    if (has2Clubs) {
      trickLeader.value = i
      currentPlayer.value = i
      break
    }
  }
}

const toggleCardSelection = (index: number) => {
  if (selectedForPassing.value.includes(index)) {
    selectedForPassing.value = selectedForPassing.value.filter(i => i !== index)
  } else if (selectedForPassing.value.length < 3) {
    selectedForPassing.value.push(index)
  }
}

const confirmPass = () => {
  if (selectedForPassing.value.length !== 3) return
  
  // Pass cards (simplified - just remove from player hand)
  const cardsToPass = selectedForPassing.value.map(index => playerHand.value[index])
  selectedForPassing.value.sort((a, b) => b - a).forEach(index => {
    playerHand.value.splice(index, 1)
  })
  
  // Add random cards back (simulating receiving passed cards)
  const deck = createDeck()
  for (let i = 0; i < 3; i++) {
    playerHand.value.push(deck.pop()!)
  }
  
  // Sort hand again
  playerHand.value.sort((a, b) => {
    if (a.suit !== b.suit) {
      return suits.indexOf(a.suit) - suits.indexOf(b.suit)
    }
    return a.value - b.value
  })
  
  selectedForPassing.value = []
  gamePhase.value = 'playing'
}

const canPlayCard = (card: Card): boolean => {
  if (currentPlayer.value !== 0) return false
  
  const leadSuit = currentTrickCards.value.find(c => c !== null)?.suit
  
  // First trick must start with 2 of clubs
  if (currentTrick.value === 1 && currentTrickCards.value.every(c => c === null)) {
    return card.suit === '♣' && card.rank === '2'
  }
  
  // Must follow suit if possible
  if (leadSuit) {
    const hasSuit = playerHand.value.some(c => c.suit === leadSuit)
    if (hasSuit && card.suit !== leadSuit) return false
  }
  
  // Can't play hearts or Queen of Spades on first trick
  if (currentTrick.value === 1) {
    if (card.suit === '♥' || (card.suit === '♠' && card.rank === 'Q')) {
      return false
    }
  }
  
  // Can't lead hearts unless broken
  if (!leadSuit && card.suit === '♥' && !heartsBroken.value) {
    const hasNonHearts = playerHand.value.some(c => c.suit !== '♥')
    if (hasNonHearts) return false
  }
  
  return true
}

const playCard = (cardIndex: number) => {
  if (!canPlayCard(playerHand.value[cardIndex])) return
  
  const card = playerHand.value.splice(cardIndex, 1)[0]
  currentTrickCards.value[0] = card
  
  if (card.suit === '♥') heartsBroken.value = true
  
  currentPlayer.value = 1
  setTimeout(computerPlay, 1000)
}

const computerPlay = () => {
  if (currentPlayer.value === 0) return
  
  const player = players.value[currentPlayer.value]
  let cardToPlay: Card
  
  // Simple AI: play a random valid card
  const validCards = player.hand.filter(card => {
    const leadSuit = currentTrickCards.value.find(c => c !== null)?.suit
    if (leadSuit) {
      const hasSuit = player.hand.some(c => c.suit === leadSuit)
      if (hasSuit) return card.suit === leadSuit
    }
    return true
  })
  
  cardToPlay = validCards[Math.floor(Math.random() * validCards.length)]
  
  const cardIndex = player.hand.indexOf(cardToPlay)
  player.hand.splice(cardIndex, 1)
  currentTrickCards.value[currentPlayer.value] = cardToPlay
  
  if (cardToPlay.suit === '♥') heartsBroken.value = true
  
  currentPlayer.value = (currentPlayer.value + 1) % 4
  
  if (currentPlayer.value === trickLeader.value) {
    // Trick complete
    setTimeout(completeTrick, 1000)
  } else {
    setTimeout(computerPlay, 1000)
  }
}

const completeTrick = () => {
  // Determine trick winner
  const leadSuit = currentTrickCards.value.find(c => c !== null)!.suit
  let winnerIndex = 0
  let highestValue = 0
  
  currentTrickCards.value.forEach((card, index) => {
    if (card && card.suit === leadSuit && card.value > highestValue) {
      highestValue = card.value
      winnerIndex = index
    }
  })
  
  // Add trick to winner's taken tricks
  players.value[winnerIndex].tricksTaken.push([...currentTrickCards.value.filter(c => c !== null)] as Card[])
  
  // Clear trick
  currentTrickCards.value = [null, null, null, null]
  trickLeader.value = winnerIndex
  currentPlayer.value = winnerIndex
  currentTrick.value++
  
  if (currentTrick.value > 13) {
    endRound()
  } else if (currentPlayer.value !== 0) {
    setTimeout(computerPlay, 1000)
  }
}

const endRound = () => {
  // Calculate scores
  players.value.forEach(player => {
    let roundScore = 0
    player.tricksTaken.forEach(trick => {
      trick.forEach(card => {
        if (card.suit === '♥') roundScore += 1
        if (card.suit === '♠' && card.rank === 'Q') roundScore += 13
      })
    })
    player.roundScore = roundScore
    player.score += roundScore
  })
  
  gamePhase.value = 'roundEnd'
}

const nextRound = () => {
  if (players.value.some(player => player.score >= 100)) {
    gamePhase.value = 'gameOver'
    emit('game-over', { rounds: currentRound.value, winner: winner.value })
    return
  }
  
  currentRound.value++
  currentTrick.value = 1
  heartsBroken.value = false
  
  if (passDirection.value === 'no passing') {
    dealCards()
    gamePhase.value = 'playing'
  } else {
    dealCards()
    gamePhase.value = 'passing'
  }
}

const newGame = () => {
  resetGame()
}

const resetGame = () => {
  currentRound.value = 1
  currentTrick.value = 1
  gamePhase.value = 'passing'
  heartsBroken.value = false
  
  players.value.forEach(player => {
    player.score = 0
    player.roundScore = 0
    player.hand = []
    player.tricksTaken = []
  })
  
  selectedForPassing.value = []
  currentTrickCards.value = [null, null, null, null]
  
  dealCards()
}

onMounted(() => {
  dealCards()
})
</script>

<style scoped>
.hearts-game {
  user-select: none;
}

.playing-card.ring-2 {
  transform: translateY(-8px);
}
</style>

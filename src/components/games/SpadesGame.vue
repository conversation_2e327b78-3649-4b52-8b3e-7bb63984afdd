<template>
  <div class="spades-game h-full bg-green-800 p-4">
    <!-- Game Header -->
    <div class="flex justify-between items-center mb-6 text-white">
      <div class="flex items-center space-x-6">
        <span>Round: {{ currentRound }}</span>
        <span>Trick: {{ currentTrick }}/13</span>
        <span v-if="gamePhase === 'bidding'">Bidding Phase</span>
        <span v-else-if="gamePhase === 'playing'">Playing Phase</span>
      </div>
      <button
        @click="newGame"
        class="bg-green-900 hover:bg-green-950 px-4 py-2 rounded text-white transition-colors"
      >
        New Game
      </button>
    </div>

    <!-- Team Scores -->
    <div class="scores-section mb-6 bg-white rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-2">Team Scores</h3>
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center p-4 bg-blue-100 rounded">
          <div class="font-bold text-lg">You & South</div>
          <div class="text-2xl font-bold text-blue-600">{{ teamScores[0] }}</div>
          <div class="text-sm text-gray-600">Bid: {{ teamBids[0] }} | Made: {{ teamTricks[0] }}</div>
        </div>
        <div class="text-center p-4 bg-red-100 rounded">
          <div class="font-bold text-lg">East & West</div>
          <div class="text-2xl font-bold text-red-600">{{ teamScores[1] }}</div>
          <div class="text-sm text-gray-600">Bid: {{ teamBids[1] }} | Made: {{ teamTricks[1] }}</div>
        </div>
      </div>
    </div>

    <!-- Bidding Phase -->
    <div v-if="gamePhase === 'bidding'" class="bidding-section mb-6">
      <div class="bg-white rounded-lg p-4 mb-4">
        <h3 class="text-lg font-semibold mb-2">Bidding</h3>
        <div class="grid grid-cols-4 gap-4 mb-4">
          <div v-for="(player, index) in players" :key="index" class="text-center p-2 rounded" 
               :class="index === currentBidder ? 'bg-yellow-100' : 'bg-gray-100'">
            <div class="font-medium">{{ player.name }}</div>
            <div class="text-lg font-bold">{{ player.bid !== null ? player.bid : '?' }}</div>
          </div>
        </div>
      </div>
      
      <div v-if="currentBidder === 0" class="player-bidding bg-white rounded-lg p-4">
        <h4 class="font-semibold mb-2">Your Bid (Spades in hand: {{ spadesCount }})</h4>
        <div class="flex items-center space-x-4">
          <div class="flex space-x-2">
            <button
              v-for="bid in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]"
              :key="bid"
              @click="makeBid(bid)"
              class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded transition-colors text-sm"
            >
              {{ bid }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Playing Phase -->
    <div v-if="gamePhase === 'playing'" class="playing-section">
      <!-- Current Trick -->
      <div class="trick-area mb-6">
        <h3 class="text-white text-lg mb-4 text-center">Current Trick (Leader: {{ players[trickLeader].name }})</h3>
        <div class="relative w-80 h-80 mx-auto bg-green-600 rounded-full border-4 border-white">
          <!-- North -->
          <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
            <PlayingCard
              v-if="currentTrickCards[1]"
              :card="currentTrickCards[1]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[1].name }}</div>
          </div>
          
          <!-- East -->
          <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
            <PlayingCard
              v-if="currentTrickCards[2]"
              :card="currentTrickCards[2]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[2].name }}</div>
          </div>
          
          <!-- South (Player) -->
          <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <PlayingCard
              v-if="currentTrickCards[0]"
              :card="currentTrickCards[0]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[0].name }}</div>
          </div>
          
          <!-- West -->
          <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
            <PlayingCard
              v-if="currentTrickCards[3]"
              :card="currentTrickCards[3]"
              :draggable="false"
            />
            <div v-else class="w-20 h-28 border-2 border-dashed border-white rounded-lg"></div>
            <div class="text-white text-xs text-center mt-1">{{ players[3].name }}</div>
          </div>
        </div>
      </div>

      <!-- Player Hand -->
      <div class="player-hand-section">
        <h3 class="text-white text-lg mb-4 text-center">Your Hand</h3>
        <div class="flex flex-wrap justify-center gap-2">
          <PlayingCard
            v-for="(card, index) in playerHand"
            :key="`hand-${index}`"
            :card="card"
            :draggable="false"
            :class="{ 
              'cursor-pointer hover:transform hover:-translate-y-2 transition-transform': canPlayCard(card),
              'opacity-50 cursor-not-allowed': !canPlayCard(card)
            }"
            @click="playCard(index)"
          />
        </div>
      </div>
    </div>

    <!-- Round End -->
    <div v-if="gamePhase === 'roundEnd'" class="round-end-section">
      <div class="bg-white rounded-lg p-6 text-center mb-6">
        <h2 class="text-2xl font-bold mb-4">Round {{ currentRound }} Complete</h2>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="p-4 bg-blue-100 rounded">
            <div class="font-bold">You & South</div>
            <div class="text-sm">Bid: {{ teamBids[0] }} | Made: {{ teamTricks[0] }}</div>
            <div class="text-lg font-bold">{{ roundScores[0] > 0 ? '+' : '' }}{{ roundScores[0] }} points</div>
          </div>
          <div class="p-4 bg-red-100 rounded">
            <div class="font-bold">East & West</div>
            <div class="text-sm">Bid: {{ teamBids[1] }} | Made: {{ teamTricks[1] }}</div>
            <div class="text-lg font-bold">{{ roundScores[1] > 0 ? '+' : '' }}{{ roundScores[1] }} points</div>
          </div>
        </div>
        <button
          @click="nextRound"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded transition-colors"
        >
          Next Round
        </button>
      </div>
    </div>

    <!-- Game Over -->
    <div v-if="gamePhase === 'gameOver'" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 text-center">
        <h2 class="text-2xl font-bold mb-4" :class="teamScores[0] >= 500 ? 'text-green-600' : 'text-red-600'">
          {{ teamScores[0] >= 500 ? 'You Win!' : 'You Lose!' }}
        </h2>
        <div class="mb-4">
          <div class="text-lg">Final Scores:</div>
          <div class="text-xl font-bold text-blue-600">You & South: {{ teamScores[0] }}</div>
          <div class="text-xl font-bold text-red-600">East & West: {{ teamScores[1] }}</div>
        </div>
        <p class="text-gray-600 mb-4">Game completed in {{ currentRound }} rounds</p>
        <button
          @click="resetGame"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded transition-colors"
        >
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import PlayingCard from './PlayingCard.vue'

const emit = defineEmits<{
  'game-over': [result: any]
}>()

interface Card {
  suit: string
  rank: string
  faceUp: boolean
  color: 'red' | 'black'
  value: number
}

interface Player {
  name: string
  hand: Card[]
  bid: number | null
  tricksTaken: number
}

const currentRound = ref(1)
const currentTrick = ref(1)
const gamePhase = ref<'bidding' | 'playing' | 'roundEnd' | 'gameOver'>('bidding')

const players = ref<Player[]>([
  { name: 'You', hand: [], bid: null, tricksTaken: 0 },
  { name: 'North', hand: [], bid: null, tricksTaken: 0 },
  { name: 'East', hand: [], bid: null, tricksTaken: 0 },
  { name: 'West', hand: [], bid: null, tricksTaken: 0 }
])

const teamScores = ref([0, 0]) // [Team 0 (You+South), Team 1 (East+West)]
const teamBids = ref([0, 0])
const teamTricks = ref([0, 0])
const roundScores = ref([0, 0])

const currentBidder = ref(0)
const currentPlayer = ref(0)
const trickLeader = ref(0)
const currentTrickCards = ref<(Card | null)[]>([null, null, null, null])
const spadesBroken = ref(false)

const suits = ['♣', '♦', '♥', '♠']
const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']

const playerHand = computed(() => players.value[0].hand)

const spadesCount = computed(() => {
  return playerHand.value.filter(card => card.suit === '♠').length
})

const createDeck = (): Card[] => {
  const deck: Card[] = []
  suits.forEach(suit => {
    ranks.forEach((rank, index) => {
      deck.push({
        suit,
        rank,
        faceUp: true,
        color: suit === '♥' || suit === '♦' ? 'red' : 'black',
        value: index + 2
      })
    })
  })
  return shuffleDeck(deck)
}

const shuffleDeck = (deck: Card[]): Card[] => {
  const shuffled = [...deck]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const dealCards = () => {
  const deck = createDeck()
  
  // Clear hands and reset
  players.value.forEach(player => {
    player.hand = []
    player.bid = null
    player.tricksTaken = 0
  })
  
  teamBids.value = [0, 0]
  teamTricks.value = [0, 0]
  spadesBroken.value = false
  
  // Deal 13 cards to each player
  for (let i = 0; i < 13; i++) {
    players.value.forEach(player => {
      player.hand.push(deck.pop()!)
    })
  }
  
  // Sort hands
  players.value.forEach(player => {
    player.hand.sort((a, b) => {
      if (a.suit !== b.suit) {
        return suits.indexOf(a.suit) - suits.indexOf(b.suit)
      }
      return a.value - b.value
    })
  })
  
  currentBidder.value = 0
  gamePhase.value = 'bidding'
}

const makeBid = (bid: number) => {
  players.value[0].bid = bid
  currentBidder.value = 1
  setTimeout(computerBid, 1000)
}

const computerBid = () => {
  if (currentBidder.value === 0) return
  
  const player = players.value[currentBidder.value]
  // Simple AI bidding based on spades count
  const spadesCount = player.hand.filter(card => card.suit === '♠').length
  const highSpades = player.hand.filter(card => card.suit === '♠' && card.value >= 11).length
  
  let bid = Math.max(0, spadesCount - 3 + highSpades)
  bid = Math.min(bid, 7) // Cap at reasonable bid
  
  player.bid = bid
  currentBidder.value = (currentBidder.value + 1) % 4
  
  if (currentBidder.value === 0) {
    // All bids complete
    calculateTeamBids()
    startPlaying()
  } else {
    setTimeout(computerBid, 1000)
  }
}

const calculateTeamBids = () => {
  teamBids.value[0] = (players.value[0].bid || 0) + (players.value[2].bid || 0) // You + South
  teamBids.value[1] = (players.value[1].bid || 0) + (players.value[3].bid || 0) // North + East
}

const startPlaying = () => {
  gamePhase.value = 'playing'
  currentTrick.value = 1
  currentPlayer.value = 0 // Player starts
  trickLeader.value = 0
  currentTrickCards.value = [null, null, null, null]
}

const canPlayCard = (card: Card): boolean => {
  if (currentPlayer.value !== 0) return false
  
  const leadSuit = currentTrickCards.value.find(c => c !== null)?.suit
  
  // Must follow suit if possible
  if (leadSuit) {
    const hasSuit = playerHand.value.some(c => c.suit === leadSuit)
    if (hasSuit && card.suit !== leadSuit) return false
  }
  
  // Can't lead spades unless broken or only spades left
  if (!leadSuit && card.suit === '♠' && !spadesBroken.value) {
    const hasNonSpades = playerHand.value.some(c => c.suit !== '♠')
    if (hasNonSpades) return false
  }
  
  return true
}

const playCard = (cardIndex: number) => {
  if (!canPlayCard(playerHand.value[cardIndex])) return
  
  const card = playerHand.value.splice(cardIndex, 1)[0]
  currentTrickCards.value[0] = card
  
  if (card.suit === '♠') spadesBroken.value = true
  
  currentPlayer.value = 1
  setTimeout(computerPlay, 1000)
}

const computerPlay = () => {
  if (currentPlayer.value === 0) return
  
  const player = players.value[currentPlayer.value]
  let cardToPlay: Card
  
  // Simple AI: follow suit if possible, otherwise play lowest card
  const leadSuit = currentTrickCards.value.find(c => c !== null)?.suit
  
  if (leadSuit) {
    const suitCards = player.hand.filter(card => card.suit === leadSuit)
    if (suitCards.length > 0) {
      cardToPlay = suitCards[0] // Play lowest of suit
    } else {
      cardToPlay = player.hand[0] // Play lowest card
    }
  } else {
    // Leading - avoid spades unless broken
    if (!spadesBroken.value) {
      const nonSpades = player.hand.filter(card => card.suit !== '♠')
      cardToPlay = nonSpades.length > 0 ? nonSpades[0] : player.hand[0]
    } else {
      cardToPlay = player.hand[0]
    }
  }
  
  const cardIndex = player.hand.indexOf(cardToPlay)
  player.hand.splice(cardIndex, 1)
  currentTrickCards.value[currentPlayer.value] = cardToPlay
  
  if (cardToPlay.suit === '♠') spadesBroken.value = true
  
  currentPlayer.value = (currentPlayer.value + 1) % 4
  
  if (currentPlayer.value === trickLeader.value) {
    // Trick complete
    setTimeout(completeTrick, 1000)
  } else {
    setTimeout(computerPlay, 1000)
  }
}

const completeTrick = () => {
  // Determine trick winner
  const leadSuit = currentTrickCards.value.find(c => c !== null)!.suit
  let winnerIndex = 0
  let highestValue = 0
  let hasSpade = false
  
  currentTrickCards.value.forEach((card, index) => {
    if (card) {
      if (card.suit === '♠' && leadSuit !== '♠') {
        if (!hasSpade || card.value > highestValue) {
          highestValue = card.value
          winnerIndex = index
          hasSpade = true
        }
      } else if (!hasSpade && card.suit === leadSuit && card.value > highestValue) {
        highestValue = card.value
        winnerIndex = index
      }
    }
  })
  
  players.value[winnerIndex].tricksTaken++
  
  // Update team tricks
  if (winnerIndex === 0 || winnerIndex === 2) {
    teamTricks.value[0]++
  } else {
    teamTricks.value[1]++
  }
  
  // Clear trick
  currentTrickCards.value = [null, null, null, null]
  trickLeader.value = winnerIndex
  currentPlayer.value = winnerIndex
  currentTrick.value++
  
  if (currentTrick.value > 13) {
    endRound()
  } else if (currentPlayer.value !== 0) {
    setTimeout(computerPlay, 1000)
  }
}

const endRound = () => {
  // Calculate round scores
  roundScores.value = [0, 0]
  
  for (let team = 0; team < 2; team++) {
    if (teamTricks.value[team] >= teamBids.value[team]) {
      // Made bid
      roundScores.value[team] = teamBids.value[team] * 10 + (teamTricks.value[team] - teamBids.value[team])
    } else {
      // Failed bid
      roundScores.value[team] = -teamBids.value[team] * 10
    }
    
    teamScores.value[team] += roundScores.value[team]
  }
  
  gamePhase.value = 'roundEnd'
}

const nextRound = () => {
  if (teamScores.value[0] >= 500 || teamScores.value[1] >= 500) {
    gamePhase.value = 'gameOver'
    const winner = teamScores.value[0] >= 500 ? 'player' : 'computer'
    emit('game-over', { rounds: currentRound.value, winner, scores: teamScores.value })
    return
  }
  
  currentRound.value++
  currentTrick.value = 1
  teamTricks.value = [0, 0]
  
  dealCards()
}

const newGame = () => {
  resetGame()
}

const resetGame = () => {
  currentRound.value = 1
  currentTrick.value = 1
  gamePhase.value = 'bidding'
  teamScores.value = [0, 0]
  teamTricks.value = [0, 0]
  spadesBroken.value = false
  
  players.value.forEach(player => {
    player.hand = []
    player.bid = null
    player.tricksTaken = 0
  })
  
  currentTrickCards.value = [null, null, null, null]
  
  dealCards()
}

onMounted(() => {
  dealCards()
})
</script>

<style scoped>
.spades-game {
  user-select: none;
}
</style>

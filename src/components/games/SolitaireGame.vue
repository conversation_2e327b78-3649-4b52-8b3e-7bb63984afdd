<template>
  <div class="solitaire-game h-full bg-green-600 p-4">
    <!-- Game Header -->
    <div class="flex justify-between items-center mb-4 text-white">
      <div class="flex items-center space-x-4">
        <span>Score: {{ score }}</span>
        <span>Moves: {{ moves }}</span>
        <span>Time: {{ formatTime(gameTime) }}</span>
      </div>
      <button
        @click="newGame"
        class="bg-green-700 hover:bg-green-800 px-4 py-2 rounded text-white transition-colors"
      >
        New Game
      </button>
    </div>

    <!-- Game Board -->
    <div class="game-board">
      <!-- Foundation Piles (Top Right) -->
      <div class="foundations flex space-x-2 mb-4">
        <div
          v-for="(foundation, index) in foundations"
          :key="`foundation-${index}`"
          class="foundation-pile w-20 h-28 border-2 border-dashed border-white rounded-lg flex items-center justify-center"
          @drop="dropCard($event, 'foundation', index)"
          @dragover.prevent
        >
          <PlayingCard
            v-if="foundation.length > 0"
            :card="foundation[foundation.length - 1]"
            :draggable="false"
          />
          <div v-else class="text-white text-xs text-center">
            {{ ['♠', '♥', '♦', '♣'][index] }}
          </div>
        </div>
      </div>

      <!-- Stock and Waste Piles -->
      <div class="stock-waste flex space-x-2 mb-4">
        <div
          class="stock-pile w-20 h-28 border-2 border-dashed border-white rounded-lg flex items-center justify-center cursor-pointer"
          @click="drawCard"
        >
          <PlayingCard
            v-if="stock.length > 0"
            :card="{ suit: '', rank: '', faceUp: false }"
            :draggable="false"
          />
          <div v-else class="text-white text-xs">Empty</div>
        </div>
        
        <div
          class="waste-pile w-20 h-28 border-2 border-dashed border-white rounded-lg flex items-center justify-center"
        >
          <PlayingCard
            v-if="waste.length > 0"
            :card="waste[waste.length - 1]"
            :draggable="true"
            @dragstart="startDrag($event, waste[waste.length - 1], 'waste', waste.length - 1)"
          />
        </div>
      </div>

      <!-- Tableau Piles -->
      <div class="tableau grid grid-cols-7 gap-2">
        <div
          v-for="(pile, pileIndex) in tableau"
          :key="`tableau-${pileIndex}`"
          class="tableau-pile min-h-32"
          @drop="dropCard($event, 'tableau', pileIndex)"
          @dragover.prevent
        >
          <div
            v-for="(card, cardIndex) in pile"
            :key="`${pileIndex}-${cardIndex}`"
            class="relative"
            :style="{ marginTop: cardIndex > 0 ? '20px' : '0' }"
          >
            <PlayingCard
              :card="card"
              :draggable="card.faceUp"
              @dragstart="startDrag($event, card, 'tableau', pileIndex, cardIndex)"
              @click="flipCard(pileIndex, cardIndex)"
            />
          </div>
          <div
            v-if="pile.length === 0"
            class="w-20 h-28 border-2 border-dashed border-white rounded-lg flex items-center justify-center text-white text-xs"
          >
            King Only
          </div>
        </div>
      </div>
    </div>

    <!-- Win Message -->
    <div
      v-if="gameWon"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-8 text-center">
        <h2 class="text-2xl font-bold text-green-600 mb-4">Congratulations!</h2>
        <p class="text-gray-600 mb-4">You won in {{ moves }} moves and {{ formatTime(gameTime) }}!</p>
        <button
          @click="newGame"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded transition-colors"
        >
          Play Again
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import PlayingCard from './PlayingCard.vue'

const emit = defineEmits<{
  'game-over': [result: any]
}>()

interface Card {
  suit: string
  rank: string
  faceUp: boolean
  color: 'red' | 'black'
}

const score = ref(0)
const moves = ref(0)
const gameTime = ref(0)
const gameWon = ref(false)
let gameTimer: number | null = null

const stock = ref<Card[]>([])
const waste = ref<Card[]>([])
const foundations = ref<Card[][]>([[], [], [], []])
const tableau = ref<Card[][]>([[], [], [], [], [], [], []])

const suits = ['♠', '♥', '♦', '♣']
const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

const createDeck = (): Card[] => {
  const deck: Card[] = []
  suits.forEach(suit => {
    ranks.forEach(rank => {
      deck.push({
        suit,
        rank,
        faceUp: false,
        color: suit === '♥' || suit === '♦' ? 'red' : 'black'
      })
    })
  })
  return shuffleDeck(deck)
}

const shuffleDeck = (deck: Card[]): Card[] => {
  const shuffled = [...deck]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const dealCards = () => {
  const deck = createDeck()
  let cardIndex = 0

  // Deal to tableau
  for (let pile = 0; pile < 7; pile++) {
    for (let card = 0; card <= pile; card++) {
      const currentCard = deck[cardIndex++]
      if (card === pile) {
        currentCard.faceUp = true
      }
      tableau.value[pile].push(currentCard)
    }
  }

  // Remaining cards go to stock
  stock.value = deck.slice(cardIndex)
}

const newGame = () => {
  score.value = 0
  moves.value = 0
  gameTime.value = 0
  gameWon.value = false
  
  stock.value = []
  waste.value = []
  foundations.value = [[], [], [], []]
  tableau.value = [[], [], [], [], [], [], []]
  
  dealCards()
  startTimer()
}

const startTimer = () => {
  if (gameTimer) clearInterval(gameTimer)
  gameTimer = setInterval(() => {
    gameTime.value++
  }, 1000)
}

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const drawCard = () => {
  if (stock.value.length > 0) {
    const card = stock.value.pop()!
    card.faceUp = true
    waste.value.push(card)
    moves.value++
  } else if (waste.value.length > 0) {
    // Reset stock from waste
    stock.value = waste.value.reverse().map(card => ({ ...card, faceUp: false }))
    waste.value = []
  }
}

const flipCard = (pileIndex: number, cardIndex: number) => {
  const pile = tableau.value[pileIndex]
  const card = pile[cardIndex]
  
  if (!card.faceUp && cardIndex === pile.length - 1) {
    card.faceUp = true
    moves.value++
    score.value += 5
  }
}

let draggedCard: Card | null = null
let dragSource: { type: string; index: number; cardIndex?: number } | null = null

const startDrag = (event: DragEvent, card: Card, sourceType: string, sourceIndex: number, cardIndex?: number) => {
  draggedCard = card
  dragSource = { type: sourceType, index: sourceIndex, cardIndex }
  event.dataTransfer!.effectAllowed = 'move'
}

const dropCard = (event: DragEvent, targetType: string, targetIndex: number) => {
  event.preventDefault()
  
  if (!draggedCard || !dragSource) return
  
  const isValidMove = validateMove(draggedCard, targetType, targetIndex)
  
  if (isValidMove) {
    // Remove card from source
    if (dragSource.type === 'waste') {
      waste.value.pop()
    } else if (dragSource.type === 'tableau') {
      const sourcePile = tableau.value[dragSource.index]
      sourcePile.splice(dragSource.cardIndex!, sourcePile.length - dragSource.cardIndex!)
    }
    
    // Add card to target
    if (targetType === 'foundation') {
      foundations.value[targetIndex].push(draggedCard)
      score.value += 10
    } else if (targetType === 'tableau') {
      tableau.value[targetIndex].push(draggedCard)
    }
    
    moves.value++
    checkWin()
  }
  
  draggedCard = null
  dragSource = null
}

const validateMove = (card: Card, targetType: string, targetIndex: number): boolean => {
  if (targetType === 'foundation') {
    const foundation = foundations.value[targetIndex]
    if (foundation.length === 0) {
      return card.rank === 'A'
    } else {
      const topCard = foundation[foundation.length - 1]
      return card.suit === topCard.suit && getRankValue(card.rank) === getRankValue(topCard.rank) + 1
    }
  } else if (targetType === 'tableau') {
    const tableauPile = tableau.value[targetIndex]
    if (tableauPile.length === 0) {
      return card.rank === 'K'
    } else {
      const topCard = tableauPile[tableauPile.length - 1]
      return card.color !== topCard.color && getRankValue(card.rank) === getRankValue(topCard.rank) - 1
    }
  }
  return false
}

const getRankValue = (rank: string): number => {
  if (rank === 'A') return 1
  if (rank === 'J') return 11
  if (rank === 'Q') return 12
  if (rank === 'K') return 13
  return parseInt(rank)
}

const checkWin = () => {
  const totalFoundationCards = foundations.value.reduce((sum, foundation) => sum + foundation.length, 0)
  if (totalFoundationCards === 52) {
    gameWon.value = true
    if (gameTimer) {
      clearInterval(gameTimer)
      gameTimer = null
    }
  }
}

onMounted(() => {
  newGame()
})

onUnmounted(() => {
  if (gameTimer) {
    clearInterval(gameTimer)
  }
})
</script>

<style scoped>
.solitaire-game {
  user-select: none;
}

.game-board {
  max-width: 800px;
  margin: 0 auto;
}
</style>

<template>
  <div class="blackjack-game h-full bg-green-700 p-4">
    <!-- Game Header -->
    <div class="flex justify-between items-center mb-6 text-white">
      <div class="flex items-center space-x-4">
        <span>Chips: ${{ chips }}</span>
        <span>Bet: ${{ currentBet }}</span>
        <span>Wins: {{ wins }}</span>
        <span>Losses: {{ losses }}</span>
      </div>
      <button
        @click="newGame"
        class="bg-green-800 hover:bg-green-900 px-4 py-2 rounded text-white transition-colors"
      >
        New Game
      </button>
    </div>

    <!-- Dealer Section -->
    <div class="dealer-section mb-8">
      <h3 class="text-white text-lg mb-2">Dealer {{ dealerScore > 0 ? `(${dealerScore})` : '' }}</h3>
      <div class="flex space-x-2">
        <PlayingCard
          v-for="(card, index) in dealerCards"
          :key="`dealer-${index}`"
          :card="gameState === 'playing' && index === 1 ? { ...card, faceUp: false } : card"
          :draggable="false"
        />
      </div>
    </div>

    <!-- Player Section -->
    <div class="player-section mb-8">
      <h3 class="text-white text-lg mb-2">Player ({{ playerScore }})</h3>
      <div class="flex space-x-2">
        <PlayingCard
          v-for="(card, index) in playerCards"
          :key="`player-${index}`"
          :card="card"
          :draggable="false"
        />
      </div>
    </div>

    <!-- Betting Section -->
    <div v-if="gameState === 'betting'" class="betting-section mb-6">
      <h3 class="text-white text-lg mb-4">Place Your Bet</h3>
      <div class="flex items-center space-x-4 mb-4">
        <button
          v-for="amount in [5, 10, 25, 50]"
          :key="amount"
          @click="setBet(amount)"
          :disabled="chips < amount"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-500 text-white px-4 py-2 rounded transition-colors"
        >
          ${{ amount }}
        </button>
      </div>
      <div class="flex items-center space-x-4">
        <input
          v-model.number="currentBet"
          type="number"
          :min="1"
          :max="chips"
          class="bg-white text-black px-3 py-2 rounded w-24"
        />
        <button
          @click="deal"
          :disabled="currentBet <= 0 || currentBet > chips"
          class="bg-red-600 hover:bg-red-700 disabled:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
        >
          Deal
        </button>
      </div>
    </div>

    <!-- Game Actions -->
    <div v-if="gameState === 'playing'" class="actions-section mb-6">
      <div class="flex space-x-4">
        <button
          @click="hit"
          :disabled="playerScore >= 21"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
        >
          Hit
        </button>
        <button
          @click="stand"
          class="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded transition-colors"
        >
          Stand
        </button>
        <button
          v-if="canDouble"
          @click="doubleDown"
          :disabled="chips < currentBet"
          class="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
        >
          Double Down
        </button>
      </div>
    </div>

    <!-- Game Result -->
    <div v-if="gameState === 'finished'" class="result-section mb-6">
      <div class="bg-white rounded-lg p-6 text-center">
        <h2 class="text-2xl font-bold mb-2" :class="resultColor">{{ gameResult }}</h2>
        <p class="text-gray-600 mb-4">{{ resultMessage }}</p>
        <button
          @click="startNewRound"
          :disabled="chips <= 0"
          class="bg-green-600 hover:bg-green-700 disabled:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
        >
          {{ chips <= 0 ? 'Game Over' : 'Next Round' }}
        </button>
      </div>
    </div>

    <!-- Game Over -->
    <div v-if="chips <= 0" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 text-center">
        <h2 class="text-2xl font-bold text-red-600 mb-4">Game Over!</h2>
        <p class="text-gray-600 mb-4">You're out of chips!</p>
        <p class="text-gray-600 mb-4">Final Stats: {{ wins }} wins, {{ losses }} losses</p>
        <button
          @click="resetGame"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded transition-colors"
        >
          Start Over
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import PlayingCard from './PlayingCard.vue'

const emit = defineEmits<{
  'game-over': [result: any]
}>()

interface Card {
  suit: string
  rank: string
  faceUp: boolean
  color: 'red' | 'black'
  value: number
}

const chips = ref(100)
const currentBet = ref(10)
const wins = ref(0)
const losses = ref(0)

const gameState = ref<'betting' | 'playing' | 'finished'>('betting')
const playerCards = ref<Card[]>([])
const dealerCards = ref<Card[]>([])
const deck = ref<Card[]>([])

const gameResult = ref('')
const resultMessage = ref('')

const suits = ['♠', '♥', '♦', '♣']
const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

const createDeck = (): Card[] => {
  const newDeck: Card[] = []
  suits.forEach(suit => {
    ranks.forEach(rank => {
      let value = parseInt(rank)
      if (rank === 'A') value = 11
      if (['J', 'Q', 'K'].includes(rank)) value = 10
      
      newDeck.push({
        suit,
        rank,
        faceUp: true,
        color: suit === '♥' || suit === '♦' ? 'red' : 'black',
        value
      })
    })
  })
  return shuffleDeck(newDeck)
}

const shuffleDeck = (deckToShuffle: Card[]): Card[] => {
  const shuffled = [...deckToShuffle]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const calculateScore = (cards: Card[]): number => {
  let score = 0
  let aces = 0
  
  cards.forEach(card => {
    if (card.rank === 'A') {
      aces++
      score += 11
    } else {
      score += card.value
    }
  })
  
  // Adjust for aces
  while (score > 21 && aces > 0) {
    score -= 10
    aces--
  }
  
  return score
}

const playerScore = computed(() => calculateScore(playerCards.value))
const dealerScore = computed(() => gameState.value === 'finished' ? calculateScore(dealerCards.value) : 0)

const canDouble = computed(() => playerCards.value.length === 2 && chips.value >= currentBet.value)

const resultColor = computed(() => {
  if (gameResult.value.includes('Win') || gameResult.value === 'Blackjack!') return 'text-green-600'
  if (gameResult.value.includes('Lose') || gameResult.value === 'Bust!') return 'text-red-600'
  return 'text-yellow-600'
})

const setBet = (amount: number) => {
  if (chips.value >= amount) {
    currentBet.value = amount
  }
}

const deal = () => {
  if (currentBet.value <= 0 || currentBet.value > chips.value) return
  
  deck.value = createDeck()
  playerCards.value = []
  dealerCards.value = []
  
  // Deal initial cards
  playerCards.value.push(deck.value.pop()!)
  dealerCards.value.push(deck.value.pop()!)
  playerCards.value.push(deck.value.pop()!)
  dealerCards.value.push(deck.value.pop()!)
  
  gameState.value = 'playing'
  
  // Check for blackjack
  if (playerScore.value === 21) {
    stand()
  }
}

const hit = () => {
  if (deck.value.length > 0) {
    playerCards.value.push(deck.value.pop()!)
    
    if (playerScore.value > 21) {
      // Player busts
      endGame('Bust!', 'You went over 21!', false)
    } else if (playerScore.value === 21) {
      stand()
    }
  }
}

const stand = () => {
  gameState.value = 'finished'
  
  // Dealer plays
  while (calculateScore(dealerCards.value) < 17) {
    if (deck.value.length > 0) {
      dealerCards.value.push(deck.value.pop()!)
    }
  }
  
  const finalPlayerScore = playerScore.value
  const finalDealerScore = calculateScore(dealerCards.value)
  
  // Determine winner
  if (finalPlayerScore === 21 && playerCards.value.length === 2) {
    // Player blackjack
    if (finalDealerScore === 21 && dealerCards.value.length === 2) {
      endGame('Push!', 'Both have blackjack!', null)
    } else {
      endGame('Blackjack!', 'You got blackjack!', true, 1.5)
    }
  } else if (finalDealerScore > 21) {
    endGame('You Win!', 'Dealer busts!', true)
  } else if (finalPlayerScore > finalDealerScore) {
    endGame('You Win!', `${finalPlayerScore} beats ${finalDealerScore}!`, true)
  } else if (finalPlayerScore < finalDealerScore) {
    endGame('You Lose!', `${finalDealerScore} beats ${finalPlayerScore}!`, false)
  } else {
    endGame('Push!', 'It\'s a tie!', null)
  }
}

const doubleDown = () => {
  if (chips.value >= currentBet.value) {
    chips.value -= currentBet.value
    currentBet.value *= 2
    hit()
    if (playerScore.value <= 21) {
      stand()
    }
  }
}

const endGame = (result: string, message: string, playerWon: boolean | null, multiplier: number = 1) => {
  gameResult.value = result
  resultMessage.value = message
  
  if (playerWon === true) {
    chips.value += Math.floor(currentBet.value * (1 + multiplier))
    wins.value++
  } else if (playerWon === false) {
    chips.value -= currentBet.value
    losses.value++
  } else {
    // Push - return bet
    // Bet was already deducted, so add it back
  }
  
  if (chips.value <= 0) {
    emit('game-over', { wins: wins.value, losses: losses.value })
  }
}

const startNewRound = () => {
  gameState.value = 'betting'
  currentBet.value = Math.min(10, chips.value)
}

const newGame = () => {
  resetGame()
}

const resetGame = () => {
  chips.value = 100
  currentBet.value = 10
  wins.value = 0
  losses.value = 0
  gameState.value = 'betting'
  playerCards.value = []
  dealerCards.value = []
}
</script>

<style scoped>
.blackjack-game {
  user-select: none;
}
</style>

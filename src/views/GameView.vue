<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Game Container -->
    <div class="w-full h-screen bg-white">
      <!-- Game Header -->
      <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="goBack"
            class="text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <h1 class="text-xl font-semibold text-gray-900">
            {{ $t(`games.${gameId}`) }}
          </h1>
        </div>
        
        <div class="flex items-center space-x-4">
          <button
            @click="restartGame"
            class="text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Game Area -->
      <div class="h-full bg-green-100">
        <component 
          :is="gameComponent" 
          v-if="gameComponent"
          @game-over="handleGameOver"
        />
        <div v-else class="flex items-center justify-center h-full">
          <div class="text-center">
            <div class="text-6xl mb-4">🃏</div>
            <h2 class="text-2xl font-semibold text-gray-900 mb-2">Game Loading...</h2>
            <p class="text-gray-600">{{ $t('common.loading') }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Game Information Sections (Below the fold) -->
    <div class="bg-white">
      <!-- Game Tags Section -->
      <section class="py-8 border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Game Tags</h3>
          <div class="flex flex-wrap gap-2">
            <span 
              v-for="tag in gameTags" 
              :key="tag"
              class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </section>

      <!-- Other Games Section -->
      <section class="py-8 border-b border-gray-200">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">Other Games You Might Like</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div 
              v-for="game in otherGames" 
              :key="game.id"
              class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer"
              @click="$router.push(`/game/${game.id}`)"
            >
              <div class="text-3xl mb-2">{{ game.icon }}</div>
              <h4 class="font-medium text-gray-900">{{ $t(`games.${game.id}`) }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ game.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Game Guide Section -->
      <section class="py-8 border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">{{ $t('games.gameDescription') }}</h3>
          <div class="prose prose-gray max-w-none">
            <p class="text-gray-600 leading-relaxed">
              {{ gameDescription }}
            </p>
            
            <h4 class="text-md font-medium text-gray-900 mt-6 mb-3">{{ $t('games.howToPlay') }}</h4>
            <ul class="text-gray-600 space-y-2">
              <li v-for="rule in gameRules" :key="rule">{{ rule }}</li>
            </ul>
            
            <h4 class="text-md font-medium text-gray-900 mt-6 mb-3">{{ $t('games.tips') }}</h4>
            <ul class="text-gray-600 space-y-2">
              <li v-for="tip in gameTips" :key="tip">{{ tip }}</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Video Recommendations Section -->
      <section class="py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">Video Tutorials</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div 
              v-for="video in videoRecommendations" 
              :key="video.id"
              class="bg-gray-100 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
            >
              <div class="aspect-video bg-gray-200 flex items-center justify-center">
                <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="p-4">
                <h4 class="font-medium text-gray-900 mb-1">{{ video.title }}</h4>
                <p class="text-sm text-gray-600">{{ video.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const gameId = computed(() => route.params.gameId as string)
const gameComponent = ref(null)

// Game data
const gameData = {
  solitaire: {
    tags: ['Single Player', 'Classic', 'Patience', 'Strategy'],
    description: 'Solitaire is a classic single-player card game that has been entertaining players for centuries.',
    rules: [
      'Move cards to build four foundation piles in suit from Ace to King',
      'Build tableau columns in descending order with alternating colors',
      'Turn over face-down cards when possible',
      'Use the stock pile when no more moves are available'
    ],
    tips: [
      'Always move Aces and Twos to foundations when available',
      'Try to expose face-down cards as quickly as possible',
      'Don\'t always play cards to foundations immediately',
      'Plan several moves ahead when possible'
    ]
  },
  blackjack: {
    tags: ['Casino', 'Strategy', 'Probability', 'Quick Game'],
    description: 'Blackjack is one of the most popular casino card games, combining luck and strategy.',
    rules: [
      'Get as close to 21 as possible without going over',
      'Beat the dealer\'s hand to win',
      'Aces count as 1 or 11, face cards count as 10',
      'Hit to take another card, stand to keep your current total'
    ],
    tips: [
      'Learn basic strategy for when to hit, stand, double, or split',
      'Never take insurance bets',
      'Manage your bankroll carefully',
      'Count cards if you want to gain an edge (where legal)'
    ]
  },
  poker: {
    tags: ['Strategy', 'Psychology', 'Multiplayer', 'Skill'],
    description: 'Poker is the ultimate card game of skill, strategy, and psychological warfare.',
    rules: [
      'Make the best five-card hand possible',
      'Bet, call, raise, or fold based on your hand strength',
      'Use community cards in Texas Hold\'em',
      'Win by having the best hand or making others fold'
    ],
    tips: [
      'Play tight-aggressive for best results',
      'Pay attention to your opponents\' betting patterns',
      'Position is crucial - play more hands in late position',
      'Don\'t play every hand - be selective'
    ]
  },
  hearts: {
    tags: ['Trick-taking', 'Strategy', '4 Players', 'Avoidance'],
    description: 'Hearts is a trick-taking game where the goal is to avoid certain penalty cards.',
    rules: [
      'Avoid taking hearts (1 point each) and Queen of Spades (13 points)',
      'Lowest score wins after someone reaches 100 points',
      'Must follow suit if possible',
      'Pass three cards before each hand'
    ],
    tips: [
      'Try to pass dangerous cards like Queen of Spades',
      'Count cards to know what\'s still in play',
      'Sometimes taking all hearts ("shooting the moon") can win',
      'Watch what other players are passing'
    ]
  },
  spades: {
    tags: ['Partnership', 'Bidding', 'Trump', 'Strategy'],
    description: 'Spades is a partnership trick-taking game with bidding and trump cards.',
    rules: [
      'Bid how many tricks your partnership will take',
      'Spades are always trump cards',
      'Must follow suit if possible',
      'Score points by making your bid exactly'
    ],
    tips: [
      'Communicate with your partner through legal plays',
      'Count high cards and spades for accurate bidding',
      'Lead low spades to draw out higher ones',
      'Keep track of what spades have been played'
    ]
  },
  bridge: {
    tags: ['Partnership', 'Complex', 'Bidding', 'Advanced'],
    description: 'Bridge is considered the most sophisticated partnership card game ever created.',
    rules: [
      'Bid to determine trump suit and contract level',
      'Declarer tries to make the contract',
      'Defenders try to prevent the contract',
      'Complex scoring system rewards skill'
    ],
    tips: [
      'Learn standard bidding conventions',
      'Count points: A=4, K=3, Q=2, J=1',
      'Communicate through bidding and card play',
      'Study common card combinations and plays'
    ]
  }
}

const gameTags = computed(() => gameData[gameId.value]?.tags || [])
const gameDescription = computed(() => gameData[gameId.value]?.description || '')
const gameRules = computed(() => gameData[gameId.value]?.rules || [])
const gameTips = computed(() => gameData[gameId.value]?.tips || [])

const otherGames = computed(() => {
  const allGames = [
    { id: 'solitaire', icon: '🃏', description: 'Classic single-player card game' },
    { id: 'blackjack', icon: '🂡', description: 'Beat the dealer at 21' },
    { id: 'poker', icon: '🃁', description: 'Strategic multiplayer card game' },
    { id: 'hearts', icon: '♥️', description: 'Avoid penalty cards' },
    { id: 'spades', icon: '♠️', description: 'Partnership bidding game' },
    { id: 'bridge', icon: '🌉', description: 'Advanced partnership game' }
  ]
  return allGames.filter(game => game.id !== gameId.value).slice(0, 3)
})

const videoRecommendations = ref([
  {
    id: 1,
    title: `How to Play ${gameId.value}`,
    description: 'Learn the basic rules and strategies'
  },
  {
    id: 2,
    title: `Advanced ${gameId.value} Tips`,
    description: 'Take your game to the next level'
  },
  {
    id: 3,
    title: `${gameId.value} Tournament Play`,
    description: 'Watch professional players in action'
  }
])

const goBack = () => {
  router.push('/')
}

const restartGame = () => {
  // Restart the current game
  window.location.reload()
}

const handleGameOver = (result: any) => {
  console.log('Game over:', result)
}

onMounted(() => {
  // Load the appropriate game component
  // This would dynamically import the game component based on gameId
  console.log(`Loading game: ${gameId.value}`)
})
</script>

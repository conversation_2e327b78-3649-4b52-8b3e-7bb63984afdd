<template>
  <div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-white rounded-lg shadow p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">Error Handling Test Page</h1>
        <p class="text-gray-600 mb-8">
          This page is for testing error handling in development. These buttons will trigger different types of errors.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            @click="triggerJavaScriptError"
            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
          >
            Trigger JavaScript Error
          </button>
          
          <button
            @click="triggerPromiseRejection"
            class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded transition-colors"
          >
            Trigger Promise Rejection
          </button>
          
          <button
            @click="triggerWebSocketError"
            class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded transition-colors"
          >
            Trigger WebSocket Error (Should be filtered)
          </button>
          
          <button
            @click="triggerNetworkError"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
          >
            Trigger Network Error
          </button>
          
          <button
            @click="triggerComponentError"
            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors"
          >
            Trigger Component Error
          </button>
          
          <button
            @click="clearErrors"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
          >
            Clear All Errors
          </button>
        </div>
        
        <div v-if="showBrokenComponent" class="mt-6">
          <BrokenComponent />
        </div>
        
        <div class="mt-8 p-4 bg-gray-50 rounded">
          <h3 class="text-lg font-medium text-gray-900 mb-2">Error Log</h3>
          <div class="space-y-2 max-h-40 overflow-y-auto">
            <div
              v-for="(error, index) in errorLog"
              :key="index"
              class="text-sm p-2 bg-white rounded border-l-4"
              :class="error.filtered ? 'border-yellow-400' : 'border-red-400'"
            >
              <div class="font-medium">{{ error.type }}</div>
              <div class="text-gray-600">{{ error.message }}</div>
              <div class="text-xs text-gray-500">{{ error.timestamp }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ErrorFilter } from '../utils/errorFilter'

const showBrokenComponent = ref(false)
const errorLog = ref<Array<{
  type: string
  message: string
  timestamp: string
  filtered: boolean
}>>([])

const logError = (type: string, message: string, filtered = false) => {
  errorLog.value.unshift({
    type,
    message,
    timestamp: new Date().toLocaleTimeString(),
    filtered
  })
}

const triggerJavaScriptError = () => {
  try {
    // @ts-ignore
    nonExistentFunction()
  } catch (error) {
    logError('JavaScript Error', (error as Error).message)
    throw error
  }
}

const triggerPromiseRejection = () => {
  const promise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error('This is a test promise rejection'))
    }, 100)
  })
  
  logError('Promise Rejection', 'Test promise rejection triggered')
  return promise
}

const triggerWebSocketError = () => {
  // 模拟WebSocket错误
  const error = new Error('WebSocket connection failed: The string did not match the expected pattern.')
  
  const filtered = ErrorFilter.shouldIgnoreError(error)
  logError('WebSocket Error', error.message, filtered)
  
  if (!filtered) {
    throw error
  } else {
    console.log('WebSocket error was filtered as expected')
  }
}

const triggerNetworkError = async () => {
  try {
    await fetch('https://nonexistent-domain-12345.com/api/test')
  } catch (error) {
    logError('Network Error', (error as Error).message)
    throw error
  }
}

const triggerComponentError = () => {
  showBrokenComponent.value = true
  logError('Component Error', 'Broken component rendered')
}

const clearErrors = () => {
  errorLog.value = []
  showBrokenComponent.value = false
}

// 监听错误事件
window.addEventListener('error', (event) => {
  const filtered = ErrorFilter.shouldIgnoreError(event.error)
  if (!filtered) {
    logError('Global Error', event.message, filtered)
  }
})

window.addEventListener('unhandledrejection', (event) => {
  const filtered = ErrorFilter.shouldIgnoreError(event.reason)
  logError('Unhandled Rejection', String(event.reason), filtered)
})
</script>

<script>
// 故意破坏的组件用于测试
const BrokenComponent = {
  template: '<div>{{ brokenData.nonExistent.property }}</div>',
  data() {
    return {
      brokenData: null
    }
  }
}

export default {
  components: {
    BrokenComponent
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-white py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          {{ $t('home.title') }}
        </h1>
        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          {{ $t('home.subtitle') }}
        </p>
      </div>
    </section>

    <!-- Featured Games Section -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
          {{ $t('home.featuredGames') }}
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <GameCard
            v-for="game in games"
            :key="game.id"
            :game="game"
          />
        </div>
      </div>
    </section>

    <!-- i18n Test Component (Development Only) -->
    <section v-if="isDev" class="py-12 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <I18nTest />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import GameCard from '../components/GameCard.vue'
import I18nTest from '../components/I18nTest.vue'

interface Game {
  id: string
  name: string
  description: string
  icon: string
  difficulty: 'Easy' | 'Medium' | 'Hard'
  players: string
  duration: number
}

const games = ref<Game[]>([
  {
    id: 'solitaire',
    name: 'Solitaire',
    description: 'Classic single-player card game. Arrange cards in descending order and alternating colors to win.',
    icon: '🃏',
    difficulty: 'Easy',
    players: '1',
    duration: 15
  },
  {
    id: 'blackjack',
    name: 'Blackjack',
    description: 'Beat the dealer by getting as close to 21 as possible without going over.',
    icon: '🂡',
    difficulty: 'Medium',
    players: '1-7',
    duration: 10
  },
  {
    id: 'poker',
    name: 'Poker',
    description: 'Texas Hold\'em poker. Make the best five-card hand to win the pot.',
    icon: '🃁',
    difficulty: 'Hard',
    players: '2-10',
    duration: 30
  },
  {
    id: 'hearts',
    name: 'Hearts',
    description: 'Avoid taking hearts and the Queen of Spades in this trick-taking game.',
    icon: '♥️',
    difficulty: 'Medium',
    players: '4',
    duration: 20
  },
  {
    id: 'spades',
    name: 'Spades',
    description: 'Partnership trick-taking game. Bid and make your contract to score points.',
    icon: '♠️',
    difficulty: 'Medium',
    players: '4',
    duration: 25
  },
  {
    id: 'bridge',
    name: 'Bridge',
    description: 'The ultimate partnership card game. Bid, play, and score in this classic game.',
    icon: '🌉',
    difficulty: 'Hard',
    players: '4',
    duration: 45
  }
])

const isDev = ref(import.meta.env.DEV)
</script>
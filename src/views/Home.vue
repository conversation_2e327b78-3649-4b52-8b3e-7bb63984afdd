<template>
  <div class="min-h-screen bg-white">
    <NavBar />
    <main class="container mx-auto px-4 py-8">
      <h1 class="text-4xl font-bold text-center mb-8">{{ $t('games.title') }}</h1>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <GameCard 
          v-for="game in games" 
          :key="game.id"
          :game="game"
          @start-game="startGame"
        />
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import NavBar from '../components/NavBar.vue'
import Footer from '../components/Footer.vue'
import GameCard from '../components/GameCard.vue'

const router = useRouter()

const games = ref([
  { id: 'poker', name: 'Poker', image: '/images/poker.jpg' },
  { id: 'blackjack', name: 'Blackjack', image: '/images/blackjack.jpg' },
  { id: 'solitaire', name: 'Sol<PERSON><PERSON>', image: '/images/solitaire.jpg' },
  { id: 'hearts', name: 'Hearts', image: '/images/hearts.jpg' },
  { id: 'spades', name: 'Spades', image: '/images/spades.jpg' },
  { id: 'bridge', name: 'Bridge', image: '/images/bridge.jpg' }
])

const startGame = (gameId: string) => {
  router.push(`/game/${gameId}`)
}
</script>
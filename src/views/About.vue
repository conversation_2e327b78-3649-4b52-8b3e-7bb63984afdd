<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-white py-16">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          {{ $t('about.title') }}
        </h1>
        <p class="text-xl text-gray-600 leading-relaxed">
          {{ $t('about.description') }}
        </p>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
          {{ $t('about.features') }}
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $t('about.feature1') }}</h3>
            <p class="text-gray-600 text-sm">All games are completely free to play with no hidden costs or subscriptions.</p>
          </div>

          <div class="text-center">
            <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $t('about.feature2') }}</h3>
            <p class="text-gray-600 text-sm">Play games offline after initial load. No internet connection required.</p>
          </div>

          <div class="text-center">
            <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $t('about.feature3') }}</h3>
            <p class="text-gray-600 text-sm">Available in 10 languages including English, Chinese, Japanese, and more.</p>
          </div>

          <div class="text-center">
            <div class="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $t('about.feature4') }}</h3>
            <p class="text-gray-600 text-sm">Optimized for all devices - desktop, tablet, and mobile.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Games Overview Section -->
    <section class="py-16 bg-white">
      <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
          Available Games
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="game in games" :key="game.id" class="bg-gray-50 rounded-lg p-6">
            <div class="text-4xl mb-4">{{ game.icon }}</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $t(`games.${game.id}`) }}</h3>
            <p class="text-gray-600 text-sm mb-4">{{ game.description }}</p>
            <div class="flex justify-between text-sm text-gray-500">
              <span>{{ game.players }} players</span>
              <span>{{ game.duration }} min</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Game {
  id: string
  name: string
  description: string
  icon: string
  difficulty: 'Easy' | 'Medium' | 'Hard'
  players: string
  duration: number
}

const games = ref<Game[]>([
  {
    id: 'solitaire',
    name: 'Solitaire',
    description: 'Classic single-player card game with simple rules and endless entertainment.',
    icon: '🃏',
    difficulty: 'Easy',
    players: '1',
    duration: 15
  },
  {
    id: 'blackjack',
    name: 'Blackjack',
    description: 'Popular casino card game where you try to beat the dealer.',
    icon: '🂡',
    difficulty: 'Medium',
    players: '1-7',
    duration: 10
  },
  {
    id: 'poker',
    name: 'Poker',
    description: 'Strategic card game that combines skill, psychology, and luck.',
    icon: '🃁',
    difficulty: 'Hard',
    players: '2-10',
    duration: 30
  },
  {
    id: 'hearts',
    name: 'Hearts',
    description: 'Trick-taking game where you want to avoid certain cards.',
    icon: '♥️',
    difficulty: 'Medium',
    players: '4',
    duration: 20
  },
  {
    id: 'spades',
    name: 'Spades',
    description: 'Partnership bidding game with trumps and strategy.',
    icon: '♠️',
    difficulty: 'Medium',
    players: '4',
    duration: 25
  },
  {
    id: 'bridge',
    name: 'Bridge',
    description: 'The most sophisticated partnership card game ever created.',
    icon: '🌉',
    difficulty: 'Hard',
    players: '4',
    duration: 45
  }
])
</script>

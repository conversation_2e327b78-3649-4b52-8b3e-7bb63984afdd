// 错误过滤器 - 用于过滤开发环境中的非关键错误
export class ErrorFilter {
  private static ignoredErrors = [
    // Vite HMR WebSocket 相关错误
    'WebSocket connection',
    'WebSocket@[native code]',
    'setupWebSocket',
    'The string did not match the expected pattern',
    
    // 其他开发环境常见的非关键错误
    'ResizeObserver loop limit exceeded',
    'Non-passive event listener',
    'Script error',
    
    // 网络相关的临时错误
    'NetworkError',
    'Failed to fetch',
    'Load failed',
    
    // 浏览器扩展相关错误
    'extension',
    'chrome-extension',
    'moz-extension'
  ]

  private static ignoredPatterns = [
    /WebSocket.*connection/i,
    /HMR.*WebSocket/i,
    /vite.*client/i,
    /ResizeObserver.*loop/i,
    /Non-passive.*event/i
  ]

  /**
   * 检查错误是否应该被忽略
   */
  static shouldIgnoreError(error: Error | string | any): boolean {
    if (!import.meta.env.DEV) {
      // 生产环境不过滤错误
      return false
    }

    const errorMessage = this.getErrorMessage(error)
    
    // 检查是否匹配忽略的错误消息
    for (const ignoredError of this.ignoredErrors) {
      if (errorMessage.includes(ignoredError)) {
        console.debug(`[ErrorFilter] Ignoring error: ${ignoredError}`)
        return true
      }
    }

    // 检查是否匹配忽略的错误模式
    for (const pattern of this.ignoredPatterns) {
      if (pattern.test(errorMessage)) {
        console.debug(`[ErrorFilter] Ignoring error pattern: ${pattern}`)
        return true
      }
    }

    return false
  }

  /**
   * 从各种错误类型中提取错误消息
   */
  private static getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error
    }

    if (error instanceof Error) {
      return error.message + (error.stack || '')
    }

    if (error && typeof error === 'object') {
      if (error.message) {
        return error.message
      }
      
      if (error.reason) {
        return this.getErrorMessage(error.reason)
      }
      
      try {
        return JSON.stringify(error)
      } catch {
        return String(error)
      }
    }

    return String(error)
  }

  /**
   * 包装错误处理函数，自动过滤不重要的错误
   */
  static wrapErrorHandler(handler: (error: any) => void) {
    return (error: any) => {
      if (!this.shouldIgnoreError(error)) {
        handler(error)
      }
    }
  }

  /**
   * 设置全局错误过滤
   */
  static setupGlobalErrorFiltering() {
    if (!import.meta.env.DEV) {
      return
    }

    // 过滤 window.onerror
    const originalOnError = window.onerror
    window.onerror = (message, source, lineno, colno, error) => {
      if (this.shouldIgnoreError(error || message)) {
        return true // 阻止默认错误处理
      }
      
      if (originalOnError) {
        return originalOnError.call(window, message, source, lineno, colno, error)
      }
      
      return false
    }

    // 过滤 unhandledrejection
    const originalOnUnhandledRejection = window.onunhandledrejection
    window.onunhandledrejection = (event) => {
      if (this.shouldIgnoreError(event.reason)) {
        event.preventDefault() // 阻止默认处理
        return
      }
      
      if (originalOnUnhandledRejection) {
        originalOnUnhandledRejection.call(window, event)
      }
    }

    console.log('[ErrorFilter] Global error filtering enabled for development')
  }

  /**
   * 清理全局错误过滤
   */
  static cleanupGlobalErrorFiltering() {
    // 这里可以添加清理逻辑，如果需要的话
    console.log('[ErrorFilter] Global error filtering cleaned up')
  }
}

// 在开发环境中自动启用错误过滤
if (import.meta.env.DEV) {
  ErrorFilter.setupGlobalErrorFiltering()
}

export default ErrorFilter

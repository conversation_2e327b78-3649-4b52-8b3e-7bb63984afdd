#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 语言文件路径
const localesDir = path.join(__dirname, '../src/i18n/locales');
const languages = ['en', 'zh', 'ja', 'ko', 'fr', 'es', 'pt', 'it', 'de', 'la'];

// 必需的键
const requiredKeys = [
  'nav.home',
  'nav.about',
  'games.solitaire.name',
  'games.solitaire.description',
  'games.blackjack.name',
  'games.blackjack.description',
  'games.poker.name',
  'games.poker.description',
  'games.hearts.name',
  'games.hearts.description',
  'games.spades.name',
  'games.spades.description',
  'games.bridge.name',
  'games.bridge.description',
  'about.title',
  'about.description',
  'about.features.title',
  'about.features.free',
  'about.features.offline',
  'about.features.responsive',
  'about.features.multilingual',
  'about.games.title',
  'about.games.description',
  'about.technology.title',
  'about.technology.description',
  'common.loading',
  'common.error',
  'common.retry',
  'common.close',
  'error.title',
  'error.description',
  'error.refresh',
  'error.goHome'
];

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

function validateLanguageFile(lang) {
  const filePath = path.join(localesDir, `${lang}.json`);
  
  if (!fs.existsSync(filePath)) {
    console.error(`❌ ${lang}.json does not exist`);
    return false;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    
    const missingKeys = [];
    const emptyKeys = [];
    
    requiredKeys.forEach(key => {
      const value = getNestedValue(data, key);
      
      if (value === undefined) {
        missingKeys.push(key);
      } else if (typeof value === 'string' && value.trim() === '') {
        emptyKeys.push(key);
      }
    });
    
    if (missingKeys.length > 0 || emptyKeys.length > 0) {
      console.error(`❌ ${lang}.json has issues:`);
      
      if (missingKeys.length > 0) {
        console.error(`  Missing keys: ${missingKeys.join(', ')}`);
      }
      
      if (emptyKeys.length > 0) {
        console.error(`  Empty keys: ${emptyKeys.join(', ')}`);
      }
      
      return false;
    } else {
      console.log(`✅ ${lang}.json is valid`);
      return true;
    }
    
  } catch (error) {
    console.error(`❌ ${lang}.json has JSON syntax error:`, error.message);
    return false;
  }
}

function main() {
  console.log('🌍 Validating i18n language files...\n');
  
  let allValid = true;
  
  languages.forEach(lang => {
    const isValid = validateLanguageFile(lang);
    if (!isValid) {
      allValid = false;
    }
  });
  
  console.log('\n' + '='.repeat(50));
  
  if (allValid) {
    console.log('🎉 All language files are valid!');
    process.exit(0);
  } else {
    console.log('💥 Some language files have issues. Please fix them.');
    process.exit(1);
  }
}

main();

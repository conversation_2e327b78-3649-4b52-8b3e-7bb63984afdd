# 错误处理指南

## 问题描述

在开发过程中遇到了以下错误：

```
Something went wrong
Promise Rejection: The string did not match the expected pattern.

Error details (development only):
WebSocket@[native code]
setupWebSocket@http://localhost:5173/@vite/client:269:33
fallback@http://localhost:5173/@vite/client:248:36
```

这是一个Vite开发服务器的WebSocket连接问题，通常由热重载(HMR)功能的WebSocket连接失败导致。

## 解决方案

### 1. 更新Vite配置

在 `vite.config.ts` 中添加了WebSocket配置：

```typescript
export default defineConfig({
  plugins: [vue()],
  server: {
    hmr: {
      port: 5174,
      host: 'localhost'
    },
    host: 'localhost',
    port: 5173
  }
})
```

### 2. 创建错误过滤器

创建了 `src/utils/errorFilter.ts` 来过滤开发环境中的非关键错误：

- WebSocket连接错误
- HMR相关错误
- 浏览器扩展错误
- 其他开发环境常见错误

### 3. 改进错误边界

创建了 `src/components/ErrorBoundary.vue` 来：

- 捕获和处理应用程序错误
- 过滤不重要的开发环境错误
- 提供用户友好的错误界面
- 支持错误恢复和重试

### 4. 全局错误处理

在 `src/components/ErrorHandler.vue` 中实现了：

- 全局错误捕获
- 错误报告和日志记录
- 开发环境错误详情显示

## 错误类型分类

### 应该显示给用户的错误：
- 真正的应用程序错误
- 网络请求失败
- 用户操作导致的错误
- 游戏逻辑错误

### 应该被过滤的错误：
- Vite HMR WebSocket错误
- 浏览器扩展相关错误
- ResizeObserver错误
- 开发工具相关错误

## 测试错误处理

在开发环境中，可以访问 `/error-test` 页面来测试错误处理功能：

1. 访问 `http://localhost:5173/error-test`
2. 点击不同的按钮触发各种类型的错误
3. 观察哪些错误被过滤，哪些错误显示给用户

## 配置选项

### 错误过滤器配置

在 `src/utils/errorFilter.ts` 中可以配置：

```typescript
private static ignoredErrors = [
  'WebSocket connection',
  'setupWebSocket',
  'The string did not match the expected pattern',
  // 添加更多需要忽略的错误
]
```

### 错误边界配置

在 `src/components/ErrorBoundary.vue` 中可以：

- 自定义错误显示界面
- 配置错误恢复策略
- 添加错误报告功能

## 生产环境注意事项

1. **错误过滤器**：在生产环境中不会过滤任何错误
2. **错误报告**：可以集成第三方错误监控服务
3. **用户体验**：提供友好的错误恢复选项

## 常见问题

### Q: WebSocket错误仍然出现怎么办？

A: 
1. 重启开发服务器
2. 清除浏览器缓存
3. 检查防火墙设置
4. 尝试使用不同的端口

### Q: 如何添加新的错误过滤规则？

A: 在 `errorFilter.ts` 的 `ignoredErrors` 或 `ignoredPatterns` 数组中添加新的规则。

### Q: 如何在生产环境中启用错误报告？

A: 在 `ErrorBoundary.vue` 中添加错误报告服务的集成代码。

## 最佳实践

1. **开发环境**：过滤非关键错误，专注于真正的问题
2. **生产环境**：记录所有错误，但提供友好的用户界面
3. **错误恢复**：提供重试和回退选项
4. **用户反馈**：允许用户报告问题

## 相关文件

- `vite.config.ts` - Vite配置
- `src/utils/errorFilter.ts` - 错误过滤器
- `src/components/ErrorBoundary.vue` - 错误边界组件
- `src/components/ErrorHandler.vue` - 全局错误处理
- `src/views/ErrorTest.vue` - 错误测试页面（仅开发环境）
